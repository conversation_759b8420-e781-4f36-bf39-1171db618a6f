<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <el-form ref="queryForm" inline size="mini">
        <el-form-item label="id">
          <el-input v-model="queryParams.id"></el-input>
        </el-form-item>
        <el-form-item label="域名">
          <el-input v-model="queryParams.domain"></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <x-select show-default v-model="queryParams.state" :options="stateOptions"></x-select>
        </el-form-item>

        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row ref="toolbar" class="table-toolbar">
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
          v-permission="['domain:add']"
          >新增</el-button
        >
      </el-row>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
      >
        <el-table-column type="index" label="序号" align="center" min-width="50"></el-table-column>
        <el-table-column
          prop="domain"
          label="域名"
          align="center"
          min-width="250"
        ></el-table-column>
        <el-table-column
          prop="weight"
          label="权重"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column prop="isRandomSLD" label="随机域名" align="center" min-width="100">
          <template v-slot="{ row }">
            <el-tag type="primary" size="medium" v-if="row.isRandomSLD == 1">随机</el-tag>
            <el-tag type="info" size="medium" v-else-if="row.isRandomSLD == 0">固定</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="state" label="状态" align="center" min-width="100">
          <template v-slot="{ row }">
            <el-tag type="success" size="medium" v-if="row.state == 1">上线</el-tag>
            <el-tag type="warning" size="medium" v-else-if="row.state == 0">下线</el-tag>
            <el-tag type="danger" size="medium" v-else>禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createOn" label="添加/修改时间" align="center" min-width="180">
          <template v-slot="{ row }">
            <span>{{ row.createOn }}</span>
            <br />
            <span>{{ row.editTime }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="warning"
              @click="handleEdit(scope.row)"
              v-permission="['domain:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
              v-permission="['domain:delete']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </x-table>
    </el-card>

    <edit-dialog ref="editDialog" @ok="handleOk" />
  </div>
</template>

<script>
import { tableHeightMixin } from '@/mixin'
import { domainApi } from '@/api'
import EditDialog from './edit'
export default {
  components: {
    EditDialog,
  },
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {
        state: null,
      },
      loading: false,
      tableData: {},
      stateOptions: [
        { label: '全部', value: null },
        { label: '上线', value: 1 },
        { label: '下线', value: 0 },
        { label: '禁用', value: -1 },
      ],
    }
  },
  methods: {
    queryReset() {
      this.queryParams = {
        state: null,
      }
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      const res = await domainApi.getList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    handleAdd() {
      this.$refs.editDialog.add()
    },
    handleEdit(row) {
      this.$refs.editDialog.edit(row)
    },
    async handleDelete(row) {
      this.$confirm('是否确认删除该数据项？', '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          const res = await domainApi.delDomain(row.id)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('删除成功')
          } else {
            this.$xMsgError('删除失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },

    handleOk() {
      this.$refs.table.refresh()
    },
  },
}
</script>
