<template>
  <el-table v-bind="$attrs" v-on="$listeners">
    <el-table-column type="index" label="序号" align="center" width="50"></el-table-column>
    <el-table-column prop="id" label="ID" align="center" width="80"></el-table-column>
    <el-table-column prop="title" label="标题" align="center" min-width="100"></el-table-column>
    <el-table-column prop="iconUrl" label="图标" align="center" width="100">
      <template v-slot="{ row }">
        <el-image fit="contain" :src="row.iconUrl" style="width: 65px; height: 65px;"></el-image>
      </template>
    </el-table-column>
    <el-table-column prop="hasNew" label="是否有小红点" align="center" width="180">
      <template v-slot="{ row }">
        <el-tag v-if="row.hasNew" type="success">是</el-tag>
        <el-tag v-else type="danger">否</el-tag>
      </template>
    </el-table-column>
    <el-table-column prop="sort" label="排序" align="center" width="100"></el-table-column>
    <el-table-column prop="tips" label="提示" align="center" width="200"></el-table-column>
    <el-table-column prop="uri" label="Uri" align="center" min-width="250"></el-table-column>
    <el-table-column label="操作" width="160" align="center">
      <template slot-scope="scope">
        <el-button
          size="mini"
          type="warning"
          @click="handleEditButton(scope.row)"
          v-permission="editPermission"
          >修改</el-button
        >
        <el-button
          size="mini"
          type="danger"
          @click="handleDeleteButton(scope.row)"
          v-permission="deletePermission"
          >删除</el-button
        >
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  inheritAttrs: false,
  props: {
    field: {
      // 该按钮在主表中字段的名称，当表中只有一种按钮时可为空
      type: String,
    },
    editPermission: {
      type: Array,
    },
    deletePermission: {
      type: Array,
    },
  },
  data() {
    return {}
  },
  methods: {
    handleEditButton(row) {
      this.$emit('edit', row, this.field)
    },
    handleDeleteButton(row) {
      this.$emit('delete', row, this.field)
    },
  },
}
</script>

<style></style>
