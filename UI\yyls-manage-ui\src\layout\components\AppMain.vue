<template>
  <section class="app-main">
    <!-- <transition name="fade-transform" mode="out-in"> -->
    <!-- <router-view :key="key"/> -->
    <!-- 缓存页面 -->
    <keep-alive :include="cachedViews" :max="1">
      <router-view :key="key" />
    </keep-alive>
    <!-- </transition> -->
  </section>
</template>

<script>
export default {
  name: 'AppMain',
  computed: {
    // 缓存页面的名称（注意是vue实例里的name，不是router的name），使用场景为index->detail，返回时不刷新index
    cachedViews() {
      // return this.$store.state.tagsView.cachedViews
      return 'UserManage_User'
    },
    key() {
      return this.$route.path
    },
  },
}
</script>

<style scoped>
.app-main {
  /*50 = navbar  */
  min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  overflow: hidden;
}
.fixed-header + .app-main {
  padding-top: 50px;
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
</style>
