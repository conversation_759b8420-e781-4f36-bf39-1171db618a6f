<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <el-form ref="queryForm" inline size="mini">
        <el-form-item label="平台">
          <x-select
            url="/options/getPlatformOptions"
            v-model="queryParams.platformId"
            showDefault
          ></x-select>
        </el-form-item>
        <el-form-item label="类目名称">
          <el-input v-model="queryParams.name"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row ref="toolbar" class="table-toolbar">
        <el-button
          size="mini"
          type="primary"
          @click="handleSync"
          v-permission="['platformCategory:sync']"
          >同步类目</el-button
        >
      </el-row>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
      >
        <el-table-column type="index" label="序号" align="center" min-width="50"></el-table-column>
        <el-table-column
          prop="platformId"
          label="平台标识"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="fatherCatId"
          label="父类目ID"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="catId"
          label="类目ID"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="name"
          label="类目名称"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="level"
          label="类目层级"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column prop="leaf" label="是否叶子类目" align="center" min-width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.leaf ? 'success' : 'primary'">{{
              scope.row.leaf ? '是' : '否'
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="enable" label="是否启用" align="center" min-width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.enable ? 'success' : 'danger'">{{
              scope.row.enable ? '启用' : '停用'
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="排序" align="center" min-width="50"></el-table-column>

        <el-table-column label="操作" width="160" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="warning"
              @click="handleEdit(scope.row)"
              v-permission="['platformCategory:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
              v-permission="['platformCategory:delete']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </x-table>
    </el-card>

    <edit-dialog ref="editDialog" @ok="handleOk" />
  </div>
</template>

<script>
import { tableHeightMixin } from '@/mixin'
import { platformCategoryApi } from '@/api'
import EditDialog from './edit'
export default {
  components: {
    EditDialog,
  },
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {
        platformId: -1,
      },
      loading: false,
      tableData: {},
    }
  },
  methods: {
    queryReset() {
      this.queryParams = {
        platformId: -1,
      }
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      const res = await platformCategoryApi.getList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    handleEdit(row) {
      this.$refs.editDialog.edit(row)
    },
    async handleDelete(row) {
      this.$confirm('是否确认删除该数据项？', '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          const res = await platformCategoryApi.delPlatformCategory(row.id)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('删除成功')
          } else {
            this.$xMsgError('删除失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },
    async handleSync() {
      const platformId = this.queryParams.platformId
      if (platformId == -1) {
        this.$xMsgError('请选择平台')
        return
      }
      this.$xloading.show()
      const res = await platformCategoryApi.syncPlatformCategory(platformId)
      this.$xloading.hide()
      if (res.code == 0) {
        this.$xMsgSuccess('类目同步成功')
      } else {
        this.$xMsgError('类目同步失败！' + res.msg)
      }
    },
    handleOk() {
      this.$refs.table.refresh()
    },
  },
}
</script>
