import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/actionConfig/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/actionConfig/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function addActionConfig(data) {
  return request({
    url: '/actionConfig/add',
    method: 'post',
    data: data,
  })
}

export function editActionConfig(data) {
  return request({
    url: '/actionConfig/edit',
    method: 'post',
    data: data,
  })
}

export function delActionConfig(id) {
  return request({
    url: '/actionConfig/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

//----------ActionConfig结束----------
