<template>
  <div class="icon-container">
    <svg-icon class="platform-icon" icon-class="wechat" v-if="platform == WXLeague" />
    <svg-icon class="platform-icon" icon-class="taobao" v-else-if="platform == '2'" />
    <svg-icon class="platform-icon" icon-class="pdd" v-else-if="platform == '3'" />
    <svg-icon class="platform-icon" icon-class="jd" v-else-if="platform == '4'" />
    <svg-icon class="platform-icon" icon-class="suning" v-else-if="platform == '5'" />
    <svg-icon class="platform-icon" icon-class="tmall" v-else-if="platform == '6'" />
    <span class="platform-icon" v-else>{{ platform }}</span>
  </div>
</template>

<script>
import { PLATFORM } from '@/utils/constant'
export default {
  props: {
    platform: String,
  },
  data() {
    return {
      WXLeague: PLATFORM.WXLeague,
    }
  },
}
</script>

<style lang="scss" scoped>
$platform-icon: 30px;

.icon-container {
  display: inline-block;
}

.platform-icon {
  width: $platform-icon;
  height: $platform-icon;
}
</style>
