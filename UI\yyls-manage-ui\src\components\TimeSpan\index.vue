<template>
  <el-form-item label="时段">
    <x-radio
      v-model="curreValue"
      button
      :options="timeSpanOptions"
      @change="onTimeSpanChange"
    ></x-radio>
  </el-form-item>
</template>

<script>
export default {
  name: 'TimeSpan',
  props: ['value'],
  data() {
    return {
      timeSpanOptions: [
        { label: '1分钟', value: 1 },
        { label: '5分钟', value: 5 },
        { label: '30分钟', value: 30 },
        { label: '小时', value: 60 },
        { label: '天', value: 1440 },
      ],
      curreValue: 1,
    }
  },
  created() {
    this.curreValue = this.value
  },
  methods: {
    onTimeSpanChange(value) {
      this.$emit('onTimeSpanChange', value)
    },
  },
}
</script>
