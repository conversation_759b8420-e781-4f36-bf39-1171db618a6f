import { mapState } from 'vuex'

const mixinDevice = {
  computed: {
    ...mapState({
      device: (state) => state.app.device,
    }),
  },
  mounted() {
    // console.log(this.device)
    // 适配编辑弹窗的宽度
    if (!this.noFitEditDialogWidth) {
      if (this.isMobile()) {
        this.dialogWidth = '100%'
      } else {
        this.dialogWidth = '800px'
      }
    }
  },
  methods: {
    isMobile() {
      return this.device == 'mobile'
    },
    isDesktop() {
      return this.device == 'desktop'
    },
  },
}

import tableHeightMixin from '@/mixin/table-height'
import queryParamsMixin from '@/mixin/query-params'

export { mixinDevice, tableHeightMixin, queryParamsMixin }
