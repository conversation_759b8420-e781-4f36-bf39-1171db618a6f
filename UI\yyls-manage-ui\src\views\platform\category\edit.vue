<template>
  <x-dialog
    :title="title"
    :visible.sync="visible"
    v-loading="loading"
    @submit="submitForm"
    @cancel="close"
  >
    <el-form ref="form" :model="form" label-width="100px" size="medium">
      <el-row>
        <el-col :span="24">
          <el-form-item label="平台" prop="platformId">
            <x-select url="/options/getPlatformOptions" v-model="form.platformId"></x-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="父类目" prop="fatherCatId">
            <el-input v-model="form.fatherCatId" placeholder="请输入父类目ID" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="类目" prop="catId">
            <el-input v-model="form.catId" placeholder="请输入类目ID" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="类目名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入类目名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="类目等级" prop="level">
            <el-input-number v-model="form.level" placeholder="请输入类目等级" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="sort">
            <el-input-number v-model="form.sort" placeholder="请输入排序 数字越大越排在前" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="叶子类目" prop="leaf">
            <el-radio-group v-model="form.leaf">
              <el-radio-button :label="true">是</el-radio-button>
              <el-radio-button :label="false">否</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否启用" prop="enable">
            <el-radio-group v-model="form.enable">
              <el-radio-button :label="true">是</el-radio-button>
              <el-radio-button :label="false">否</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </x-dialog>
</template>

<script>
import { platformCategoryApi } from '@/api'
export default {
  data() {
    return {
      title: '',
      visible: false,
      loading: false,
      form: {},
    }
  },
  methods: {
    add() {
      this.reset()
      this.title = '新增'
      this.visible = true
    },
    async edit(row) {
      this.reset()
      this.title = '修改'
      this.visible = true
      this.$xloading.show()
      const res = await platformCategoryApi.getDetail(row.id)
      if (res.code == 0) {
        this.form = res.data
      }
      this.$xloading.hide()
    },

    close() {
      this.reset()
      this.visible = false
    },

    reset() {
      this.form = {}
      this.$xResetForm('form')
    },

    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        // console.log('submitForm valid: ', valid, 'data: ', this.form)
        if (valid) {
          this.$xloading.show()
          const data = Object.assign({}, this.form)
          const { id } = data
          if (id != undefined) {
            const res = await platformCategoryApi.editPlatformCategory(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('修改成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          } else {
            const res = await platformCategoryApi.addPlatformCategory(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('添加成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          }
        }
      })
    },
  },
}
</script>

<style></style>
