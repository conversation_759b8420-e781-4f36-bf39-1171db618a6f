import Vue from 'vue'

import XIcon from '@/components/XIcon'
XIcon.install = (v) => {
  v.component('x-icon', XIcon)
}
Vue.use(XIcon)

import XTable from '@/components/Table/index.js'
XTable.install = (v) => {
  v.component('x-table', XTable)
}
Vue.use(XTable)

import XRadio from '@/components/Form/Radio'
XRadio.install = (v) => {
  v.component('x-radio', XRadio)
}
Vue.use(XRadio)

import XCheckbox from '@/components/Form/Checkbox'
XCheckbox.install = (v) => {
  v.component('x-checkbox', XCheckbox)
}
Vue.use(XCheckbox)

import XSelect from '@/components/Form/Select'
XSelect.install = (v) => {
  v.component('x-select', XSelect)
}
Vue.use(XSelect)

import XDialog from '@/components/XDialog'
XDialog.install = (v) => {
  v.component('x-dialog', XDialog)
}
Vue.use(XDialog)

import GoBack from '@/components/GoBack'
GoBack.install = (v) => {
  v.component('go-back', GoBack)
}
Vue.use(GoBack)

import XCell from '@/components/XCell'
Vue.use(XCell)

import XCellGroup from '@/components/XCellGroup'
Vue.use(XCellGroup)

import TableHeaderTooltip from '@/components/Table/HeaderTooltip'
Vue.use(TableHeaderTooltip)

import XUpload from '@/components/XUpload'
XUpload.install = (v) => {
  v.component('x-upload', XUpload)
}
Vue.use(XUpload)

import XVideoUpload from '@/components/XVideoUpload'
XVideoUpload.install = (v) => {
  v.component('x-video-upload', XVideoUpload)
}
Vue.use(XVideoUpload)
