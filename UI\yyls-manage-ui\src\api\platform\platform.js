import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/platform/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/platform/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function addPlatform(data) {
  return request({
    url: '/platform/add',
    method: 'post',
    data: data,
  })
}

export function editPlatform(data) {
  return request({
    url: '/platform/edit',
    method: 'post',
    data: data,
  })
}

export function delPlatform(id) {
  return request({
    url: '/platform/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

//----------Platform结束----------
