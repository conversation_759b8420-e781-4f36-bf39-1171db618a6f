import request from '@/utils/request'

export function getList(parameter) {
  return request({
    url: '/system/user/getList',
    method: 'get',
    params: parameter,
  })
}

export function getDetail(id) {
  return request({
    url: '/system/user/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function addUser(data) {
  return request({
    url: '/system/user/add',
    method: 'post',
    data,
  })
}

export function editUser(data) {
  return request({
    url: '/system/user/edit',
    method: 'post',
    data,
  })
}

export function deleteUser(id) {
  return request({
    url: '/system/user/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

export function deleteAllUserLoginToken() {
  return request({
    url: '/system/user/deleteAllUserLoginToken',
    method: 'post',
  })
}

export function changePassword(data) {
  return request({
    url: '/system/user/changePassword',
    method: 'post',
    data,
  })
}

export function resetPassword(id) {
  return request({
    url: '/system/user/resetPassword',
    method: 'post',
    data: {
      id,
    },
  })
}

export function changeStatus(id, status) {
  return request({
    url: '/system/user/changeStatus',
    method: 'post',
    data: {
      id,
      status,
    },
  })
}

export function getRoleList() {
  return request({
    url: '/system/user/roleList',
    method: 'get',
  })
}
