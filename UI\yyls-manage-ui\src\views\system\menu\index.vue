<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->

      <el-row ref="toolbar" class="table-toolbar">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-permission="['system:menu:add']"
          >新增</el-button
        >
      </el-row>

      <!-- 表格 -->
      <el-table
        v-loading="loading"
        :data="menuList"
        row-key="id"
        :height="tableHeight"
        :tree-props="{ children: 'children' }"
      >
        <el-table-column prop="menuName" label="菜单名称" width="160"></el-table-column>
        <el-table-column prop="icon" label="图标" width="60" align="center">
          <template v-slot="{ row }">
            <!-- <i :class="row.icon" /> -->
            <x-icon :icon="row.icon" />
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="排序" width="60" align="center"></el-table-column>
        <el-table-column
          prop="component"
          label="组件路径"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="permission"
          label="权限标识"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column prop="isShow" label="可见" width="80" align="center">
          <template slot-scope="scope">
            <el-tag type="success" v-if="scope.row.isShow == '1'">显示</el-tag>
            <el-tag type="danger" v-else>隐藏</el-tag>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="weight" label="权重" width="120" align="center">
          <template slot-scope="scope">
            <el-tag type="info" v-if="scope.row.weight == 20">默认</el-tag>
            <el-tag v-if="scope.row.weight == 60">系统管理员</el-tag>
            <el-tag type="danger" v-if="scope.row.weight == 100">超级管理员</el-tag>
          </template>
        </el-table-column> -->
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              v-permission="['system:menu:edit']"
              @click="handleEdit(scope.row)"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-plus"
              v-permission="['system:menu:add']"
              @click="handleAdd(scope.row)"
              >新增</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              v-permission="['system:menu:delete']"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <edit-dialog ref="editDialog" @ok="handleOk" />
  </div>
</template>

<script>
import { tableHeightMixin } from '@/mixin'
import * as menuApi from '@/api/system/menu'
import EditDialog from './Edit'

export default {
  mixins: [tableHeightMixin],
  components: {
    EditDialog,
  },
  data() {
    return {
      tableHeightOptions: {
        page: false,
      },
      queryParams: {},
      loading: false,
      menuList: [],
    }
  },
  async created() {
    this.getList()
  },
  methods: {
    onSystemCodeChange(value) {
      this.queryParams.systemCode = value
      this.getList()
    },
    async getList() {
      this.$xloading.show()
      const res = await menuApi.getList(this.queryParams)
      this.menuList = res.data
      this.$xloading.hide()
    },
    handleOk() {
      this.getList()
    },
    handleAdd(row) {
      this.$refs.editDialog.add(row)
    },
    handleEdit(row) {
      this.$refs.editDialog.edit(row)
    },
    handleDelete(row) {
      this.$confirm('是否确认删除该数据项？', '提示', {
        type: 'warning',
      })
        .then(() => {
          this.deleteData(row)
        })
        .catch(() => {})
    },
    async deleteData(row) {
      const res = await menuApi.delMenu(row.id)
      if (res.code == 0) {
        this.$xMsgSuccess('删除成功')
        this.getList()
      } else {
        this.x.msgError('删除失败！' + res.msg)
      }
    },
  },
}
</script>

<style></style>
