<template>
  <div class="login">
    <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">
      <h3 class="title">{{this.title}}登录</h3>
      <el-form-item prop="username">
        <el-input v-model="loginForm.username" type="text" auto-complete="off" placeholder="账号">
          <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" />
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          v-model="loginForm.password"
          type="password"
          auto-complete="off"
          placeholder="密码"
          @keyup.enter.native="handleLogin"
        >
          <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
        </el-input>
      </el-form-item>
      <!-- <el-form-item prop="code">
        <el-input
          v-model="loginForm.code"
          auto-complete="off"
          placeholder="验证码"
          style="width: 63%"
          @keyup.enter.native="handleLogin"
        >
          <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
        </el-input>
        <div class="login-code">
          <img :src="codeUrl" @click="getCode" />
        </div>
      </el-form-item> -->
      <el-checkbox v-model="loginForm.rememberMe" style="margin: 0px 0px 0px 0px"
        >记住我</el-checkbox
      >
      <el-form-item style="width: 100%; margin-bottom: 10px" class="login-btn">
        <el-button
          :loading="loading"
          size="medium"
          type="primary"
          style="width: 100%"
          @click.native.prevent="handleLogin"
        >
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
      </el-form-item>
      <el-form-item v-if="isAdminUser">
        <el-button size="medium" style="width: 100%" @click.native.prevent="handleOAuth">
          <span>用户中心登录</span>
        </el-button>
      </el-form-item>
    </el-form>
    <!--  底部  -->
    <div class="el-login-footer">
      <span></span>
    </div>
  </div>
</template>

<script>
import settings from '@/settings'
import Cookies from 'js-cookie'
import { encryptRSA, commonEncryptRSA, commonDecryptRSA } from '@/utils/encrypt'
import {
  getUesrName,
  getPassword,
  getRememberMe,
  removeUserLoginParams,
  setUserName,
  setPassword,
  setRememberMe,
} from '@/utils/auth'
import { loginApi, authApi } from '@/api'

export default {
  name: 'Login',
  data() {
    const validateUsername = (rule, value, callback) => {
      if (value.length == 0) {
        callback(new Error('用户名不能为空'))
      } else {
        callback()
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (value.length == 0) {
        callback(new Error('密码不能为空'))
      } else {
        callback()
      }
    }
    return {
      title: settings.title,
      loginForm: {
        username: '',
        password: '',
      },
      loginRules: {
        username: [{ required: true, trigger: 'blur', validator: validateUsername }],
        password: [{ required: true, trigger: 'blur', validator: validatePassword }],
      },
      loading: false,
      passwordType: 'password',
      redirect: undefined,
      isAdminUser: false,
    }
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true,
    },
  },
  created() {
    // this.getCookie()
    this.checkIframe()
    this.getUserLoginParams()
    // 判断域名
    if (window.location.hostname.endsWith('dodourl.com')) {
      this.isAdminUser = true
    }
    // 直接使用优易用户中心登录
    // if (process.env.NODE_ENV == 'production') {
    //   this.handleOAuth()
    // }
    // console.log(process.env.NODE_ENV)
    // this.handleOAuth()
  },
  methods: {
    checkIframe(){
      // 检测是否被嵌入到 iframe
      // 用于判断chrome插件是否登录，没有登录就弹出登录主页
      if (window.self !== window.top) {
        let tabData = {
          url: window.location.origin
        }
        window.parent.postMessage({ type: 'OpenTab', data: tabData }, '*');
      }
    },
    getUserLoginParams() {
      const username = getUesrName()
      const password = getPassword()
      const rememberMe = getRememberMe()
      // console.log(password)
      // console.log(commonDecryptRSA(password))
      this.loginForm = {
        username: username === null ? this.loginForm.username : username,
        password: password === null ? this.loginForm.password : commonDecryptRSA(password),
        rememberMe: rememberMe === null ? false : Boolean(rememberMe),
      }
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.$xloading.show()
          let data = Object.assign({}, this.loginForm)
          // 记住我
          if (this.loginForm.rememberMe) {
            setUserName(this.loginForm.username)
            setPassword(commonEncryptRSA(this.loginForm.password))
            setRememberMe(this.loginForm.rememberMe)
          } else {
            removeUserLoginParams()
          }
          data.password = encryptRSA(data.password)
          this.$store
            .dispatch('Login', data)
            .then(() => {
              this.$router.push({ path: this.redirect || '/' })
              this.$xloading.hide()
              window.location.reload()
            })
            .catch((err) => {
              this.$xloading.hide()
              this.$xMsgError(err)
              console.log(err)
            })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    /**
     * 获取优易用户中心授权地址，并跳转
     */
    async handleOAuth() {
      let loading = this.$loading()
      const res = await authApi.getYYAuthUrl()
      loading.close()
      if (res.code == 0) {
        const authUrl = res.data
        window.location.href = authUrl
      } else {
        this.$xMsgError(res.msg)
      }
    },
  },
}
</script>

<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-color: #f7f7f7;
  // background-image: url("../assets/image/login-background.jpg");
  background-size: cover;
}
.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: #707070;
  letter-spacing: 5px;
  font-weight: normal;
}

.login-form {
  margin-top: -100px;
  border-radius: 6px;
  background: #ffffff;
  width: 450px;
  padding: 25px 25px 5px 25px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .el-input {
    height: 38px;
    input {
      height: 38px;
    }
  }
  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }

  .login-btn {
    padding-top: 20px;
  }
}
.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.login-code {
  width: 33%;
  height: 38px;
  float: right;
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
</style>
