<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
    />
    <meta name="referrer" content="no-referrer" />
    <link rel="icon" href="<%= BASE_URL %>logo.png" />
    <title><%= webpackConfig.name %></title>
    <style>
      @-webkit-keyframes spin {
        0% {
          transform: rotate(0);
        }
        100% {
          transform: rotate(360deg);
        }
      }
      @-moz-keyframes spin {
        0% {
          -moz-transform: rotate(0);
        }
        100% {
          -moz-transform: rotate(360deg);
        }
      }
      @keyframes spin {
        0% {
          transform: rotate(0);
        }
        100% {
          transform: rotate(360deg);
        }
      }
      .global-spinner {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 10000000;
        background: #000;
        opacity: 0.7;
        overflow: hidden;
      }
      .global-spinner div:first-child {
        display: block;
        position: relative;
        left: 50%;
        top: 50%;
        width: 150px;
        height: 150px;
        margin: -75px 0 0 -75px;
        border-radius: 50%;
        box-shadow: 0 3px 3px 0 #ff3d71;
        transform: translate3d(0, 0, 0);
        animation: spin 2s linear infinite;
      }
      .global-spinner div:first-child:after,
      .global-spinner div:first-child:before {
        content: "";
        position: absolute;
        border-radius: 50%;
      }
      .global-spinner div:first-child:before {
        top: 5px;
        left: 5px;
        right: 5px;
        bottom: 5px;
        box-shadow: 0 3px 3px 0 #ffaa00;
        -webkit-animation: spin 3s linear infinite;
        animation: spin 3s linear infinite;
      }
      .global-spinner div:first-child:after {
        top: 15px;
        left: 15px;
        right: 15px;
        bottom: 15px;
        box-shadow: 0 3px 3px 0 #0095ff;
        animation: spin 1.5s linear infinite;
      }
    </style>
  </head>
  <body>
    <noscript>
      <strong
        >We're sorry but <%= webpackConfig.name %> doesn't work properly without
        JavaScript enabled. Please enable it to continue.</strong
      >
    </noscript>
    <div id="app">
      <div class="global-spinner">
        <div class="blob blob-0"></div>
        <div class="blob blob-1"></div>
        <div class="blob blob-2"></div>
        <div class="blob blob-3"></div>
        <div class="blob blob-4"></div>
        <div class="blob blob-5"></div>
      </div>
    </div>
    <!-- built files will be auto injected -->
  </body>
</html>
