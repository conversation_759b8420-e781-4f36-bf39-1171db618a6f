import Vue from 'vue'
// vue 3
import VueComposition<PERSON>pi from '@vue/composition-api'
Vue.use(VueCompositionApi)

import Storage from 'vue-ls'
// vue-ls配置
const storageOptions = {
  namespace: 'yyaaf_', // key prefix
  name: 'ls', // name variable Vue.[ls] or this.[$ls],
  storage: 'local', // storage name session, local, memory
}

Vue.use(Storage, storageOptions)

import 'normalize.css/normalize.css' // A modern alternative to CSS resets

import ElementUI from 'element-ui'
// import 'element-ui/lib/theme-chalk/index.css'
import '@/styles/element-variables.scss'

import '@/styles/index.scss' // global css

import '@/components'

import GlobalLoading from '@/components/GlobalLoading'
Vue.prototype.$xloading = GlobalLoading

import App from './App'
import store from './store'
import router from './router'
import permission from './directive/permission'

import bootstrap from './core/bootstrap'
import '@/icons' // icon
import '@/permission' // permission control
import { resetForm, parseTime } from '@/utils'
import { hasPermission } from '@/utils/auth'

// 全局方法挂载

Vue.prototype.$xResetForm = resetForm

Vue.prototype.$xMsgSuccess = function (msg) {
  this.$message({ showClose: true, message: msg, type: 'success' })
}
Vue.prototype.$xMsgError = function (msg) {
  this.$message({ showClose: true, message: msg, type: 'error' })
}
Vue.prototype.$xMsgWarning = function (msg) {
  this.$message({ showClose: true, message: msg, type: 'warning' })
}
Vue.prototype.$xMsgInfo = function (msg) {
  this.$message.info(msg)
}

Vue.prototype.$hasPermission = hasPermission

// 日期格式化
Vue.prototype.$parseTime = parseTime
// 数字格式化，千分符
Vue.prototype.$xFormatInt = (num) => num.toLocaleString('en-US')

Vue.use(permission)

Vue.use(ElementUI)

Vue.config.productionTip = false

new Vue({
  el: '#app',
  router,
  store,
  created: bootstrap,
  render: (h) => h(App),
})
