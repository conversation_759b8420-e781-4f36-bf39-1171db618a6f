FROM node:12.18

ARG registry=https://registry.npm.taobao.org

RUN npm config set registry $registry
# RUN npm install -g yarn

RUN yarn config set registry https://registry.npm.taobao.org -g
RUN yarn config set sass_binary_site http://cdn.npm.taobao.org/dist/node-sass -g

RUN yarn global add @vue/cli

RUN mkdir /workspace

WORKDIR /workspace

# VOLUME /workspace

# COPY package*.json ./
# RUN yarn install

EXPOSE 9300

