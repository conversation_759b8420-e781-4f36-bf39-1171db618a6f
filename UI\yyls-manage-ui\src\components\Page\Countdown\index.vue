<template>
  <!-- 🔥 右侧浮动倒计时 -->
  <div
    class="floating-timer"
    :class="{ collapsed: isCollapsed, dragging: isDragging }"
    :style="{ top: position.y + 'px', right: position.x + 'px' }"
    @mousedown="startDrag"
    @click="handleClick"
  >
    <!-- 展开状态 -->
    <div v-if="!isCollapsed" class="timer-content">
      <span class="timer-text">下次刷新:</span>
      <span class="timer-countdown">{{ countdown }}s</span>
      <i class="el-icon-minus collapse-icon" title="收起"></i>
    </div>

    <!-- 收起状态 -->
    <div v-else class="timer-collapsed">
      <i class="el-icon-time collapse-icon" title="展开倒计时"></i>
    </div>
  </div>
</template>

<script>
export default {
  props: ['table'], // 只接收 table 组件，不传 countdown
  data() {
    return {
      countdown: 60, // 确保每个实例独立维护倒计时
      refreshTimer: null,
      isCollapsed: false, // 控制收起/展开状态
      isDragging: false, // 拖动状态
      position: { x: 20, y: window.innerHeight * 0.85 }, // 位置
      dragStart: { x: 0, y: 0 }, // 拖动开始位置
      clickTimeout: null, // 点击延时
    }
  },
  mounted() {
    this.startAutoRefresh()
    // 从本地存储恢复收起状态
    const savedState = localStorage.getItem('countdown-collapsed')
    if (savedState !== null) {
      this.isCollapsed = savedState === 'true'
    }
    // 从本地存储恢复位置
    const savedPosition = localStorage.getItem('countdown-position')
    if (savedPosition) {
      this.position = JSON.parse(savedPosition)
    }
    // 添加全局事件监听
    document.addEventListener('mousemove', this.onMouseMove)
    document.addEventListener('mouseup', this.onMouseUp)
    window.addEventListener('resize', this.onWindowResize)
  },
  beforeUnmount() {
    clearInterval(this.refreshTimer) // 组件销毁时清除定时器
    // 清理事件监听器
    document.removeEventListener('mousemove', this.onMouseMove)
    document.removeEventListener('mouseup', this.onMouseUp)
    window.removeEventListener('resize', this.onWindowResize)
    if (this.clickTimeout) {
      clearTimeout(this.clickTimeout)
    }
  },
  methods: {
    startAutoRefresh() {
      this.refreshTimer = setInterval(() => {
        if (this.countdown <= 0) {
          if (this.table) {
            this.table.refresh(false) // 确保 table 存在
          }
          this.countdown = 60 // 重新开始倒计时
        } else {
          this.countdown--
        }
      }, 1000)
    },
    stopAutoRefresh() {
      clearInterval(this.refreshTimer) // 组件销毁时清除定时器
    },
    toggleCollapse() {
      this.isCollapsed = !this.isCollapsed
      // 保存状态到本地存储
      localStorage.setItem('countdown-collapsed', this.isCollapsed)
    },

    // 处理点击事件（区分拖动和点击）
    // eslint-disable-next-line no-unused-vars
    handleClick(event) {
      if (!this.isDragging) {
        // 延迟执行点击，确保不是拖动操作
        this.clickTimeout = setTimeout(() => {
          this.toggleCollapse()
        }, 150)
      }
    },

    // 开始拖动
    startDrag(event) {
      event.preventDefault()
      this.isDragging = false
      this.dragStart = {
        x: event.clientX,
        y: event.clientY,
      }

      // 清除可能存在的点击延时
      if (this.clickTimeout) {
        clearTimeout(this.clickTimeout)
        this.clickTimeout = null
      }
    },

    // 鼠标移动
    onMouseMove(event) {
      if (this.dragStart.x !== 0 || this.dragStart.y !== 0) {
        const deltaX = event.clientX - this.dragStart.x
        const deltaY = event.clientY - this.dragStart.y

        // 如果移动距离超过阈值，认为是拖动
        if (Math.abs(deltaX) > 5 || Math.abs(deltaY) > 5) {
          this.isDragging = true

          // 计算新位置（注意right是从右边开始计算的）
          const newX = this.position.x - deltaX
          const newY = this.position.y + deltaY

          // 边界检查
          const maxX = window.innerWidth - 50
          const maxY = window.innerHeight - 50

          this.position.x = Math.max(10, Math.min(maxX, newX))
          this.position.y = Math.max(10, Math.min(maxY, newY))

          this.dragStart = {
            x: event.clientX,
            y: event.clientY,
          }
        }
      }
    },

    // 鼠标释放
    onMouseUp() {
      if (this.isDragging) {
        // 保存位置到本地存储
        localStorage.setItem('countdown-position', JSON.stringify(this.position))
      }

      // 重置拖动状态
      setTimeout(() => {
        this.isDragging = false
      }, 100)

      this.dragStart = { x: 0, y: 0 }
    },

    // 窗口大小改变时调整位置
    onWindowResize() {
      const maxX = window.innerWidth - 50
      const maxY = window.innerHeight - 50

      this.position.x = Math.max(10, Math.min(maxX, this.position.x))
      this.position.y = Math.max(10, Math.min(maxY, this.position.y))

      // 保存调整后的位置
      localStorage.setItem('countdown-position', JSON.stringify(this.position))
    },
  },
}
</script>

<style scoped>
/* 🔥 浮动倒计时 */
.floating-timer {
  position: fixed;
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  font-size: 14px;
  z-index: 999;
  cursor: move;
  transition: all 0.3s ease;
  user-select: none;
  transform: translateY(-50%);
}

.floating-timer.dragging {
  cursor: grabbing;
  transform: translateY(-50%) scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
  transition: none;
}

.floating-timer:hover:not(.dragging) {
  background: rgba(0, 0, 0, 0.8);
  transform: translateY(-50%) scale(1.05);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

/* 展开状态 */
.timer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 16px;
  position: relative;
}

.timer-text {
  font-size: 12px;
  margin-bottom: 6px;
  opacity: 0.8;
}

.timer-countdown {
  font-size: 20px;
  font-weight: bold;
  color: #ffeb3b;
  margin-bottom: 4px;
}

.collapse-icon {
  position: absolute;
  top: 4px;
  right: 6px;
  font-size: 12px;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.floating-timer:hover .collapse-icon {
  opacity: 1;
}

/* 收起状态 */
.floating-timer.collapsed {
  padding: 6px;
  min-width: 32px;
  min-height: 32px;
  border-radius: 50%;
}

.timer-collapsed {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.timer-collapsed .collapse-icon {
  font-size: 14px;
  opacity: 0.9;
  position: static;
}

/* 动画效果 */
.floating-timer.collapsed {
  animation: pulse 3s infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.7;
    transform: translateY(-50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translateY(-50%) scale(1.1);
  }
  100% {
    opacity: 0.7;
    transform: translateY(-50%) scale(1);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .timer-content {
    padding: 10px 12px;
  }

  .timer-countdown {
    font-size: 18px;
  }

  .floating-timer.collapsed {
    min-width: 28px;
    min-height: 28px;
    padding: 4px;
  }

  .timer-collapsed .collapse-icon {
    font-size: 12px;
  }
}
</style>
