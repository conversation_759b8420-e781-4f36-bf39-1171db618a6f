<template>
    <div class="table-cell-content">
        <span class="sc-id">{{ cId }}</span>
        <br />
        <span class="sc-name">{{ cName }}</span>
    </div>
</template>
  
  <script>
  export default {
    data: function () {
      return {}
    },
    props: ['cId','cName'],
    methods: {
      
    },
  }
  </script>
  
  
  
  <style scoped>
    /* 样式1：调整字体大小、行高、间距 */
  .table-cell-content {
    display: flex;
    flex-direction: column;
    align-items: center;  /* 中心对齐 */
    font-size: 14px;      /* 设置字体大小 */
    line-height: 0.8;     /* 行高 */
    word-break: break-word; /* 防止长单词溢出 */
    color: #333;         /* 设置字体颜色 */
    padding: 5px 0;      /* 设置内边距 */
  }

  .sc-id {
    font-weight: bold;   /* scId 字段加粗 */
    color: #409EFF;      /* 修改颜色 */
  }

  .sc-name {
    font-style: italic;  /* scName 字段斜体 */
    color: #67C23A;      /* 修改颜色 */
  }

  /* 样式2：给换行部分添加背景色 */
  .table-cell-content span {
    background-color: #f2f4f5; /* 设置背景色 */
    padding: 2px 4px;           /* 给文字加点内边距 */
    border-radius: 4px;         /* 设置圆角 */
  }

  </style>