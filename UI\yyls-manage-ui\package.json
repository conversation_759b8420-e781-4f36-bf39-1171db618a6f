{"name": "vue-admin-template", "version": "4.2.1", "description": "A vue admin template with Element UI & axios & iconfont & permission control & lint", "author": "yy", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build", "report": "vue-cli-service build --report", "lint": "eslint --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml"}, "dependencies": {"@riophae/vue-treeselect": "^0.4.0", "@tinymce/tinymce-vue": "^2.0.0", "@vue/composition-api": "^0.6.6", "axios": "0.18.1", "clipboard": "^2.0.6", "crypto-js": "^4.0.0", "dayjs": "^1.8.30", "echarts": "^4.7.0", "element-ui": "2.13.1", "js-cookie": "2.2.0", "jsencrypt": "^3.0.0-rc.1", "jsonp": "^0.2.1", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "qs": "^6.9.4", "tinymce": "^5.0.3", "vue": "2.6.11", "vue-json-viewer": "^2.2.11", "vue-ls": "^3.2.1", "vue-router": "3.1.6", "vuedraggable": "^2.23.2", "vuex": "3.3.0"}, "devDependencies": {"@babel/core": "7.0.0", "@babel/register": "7.0.0", "@vue/cli-plugin-babel": "3.6.0", "@vue/cli-plugin-eslint": "^3.9.1", "@vue/cli-plugin-unit-jest": "3.6.3", "@vue/cli-service": "3.6.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "^9.5.1", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "23.6.0", "babel-plugin-transform-remove-console": "^6.9.4", "chalk": "2.4.2", "connect": "3.6.6", "eslint": "5.15.3", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-vue": "5.2.2", "html-webpack-plugin": "3.2.0", "mockjs": "1.0.1-beta3", "node-sass": "^6.0.1", "prettier": "^2.0.5", "runjs": "^4.3.2", "sass-loader": "^10.2.0", "script-ext-html-webpack-plugin": "2.1.3", "script-loader": "0.7.2", "serve-static": "^1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "vue-template-compiler": "2.6.11"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}