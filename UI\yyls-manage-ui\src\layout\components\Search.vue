<template>
  <div class="search">
    <el-select
      placeholder="搜索..."
      v-model="menu"
      size="mini"
      filterable
      remote
      :remote-method="queryMenu"
      :loading="searchLoading"
      clearable
      @change="handleMenuChange"
    >
      <el-option
        v-for="item in menuOptions"
        :key="item.id"
        :label="item.menuName"
        :value="item.path"
      >
      </el-option>
    </el-select>
  </div>
</template>

<script>
import { menuApi } from '@/api'

export default {
  data() {
    return {
      menu: undefined,
      searchLoading: false,
      menuOptions: [],
    }
  },
  methods: {
    async queryMenu(menuName) {
      if (!menuName) {
        this.menuOptions = []
        return
      }
      // this.searchLoading = true
      const res = await menuApi.queryMenu(menuName)
      if (res.code == 0) {
        // console.log(res.data)
        this.menuOptions = res.data
      }
    },
    async handleMenuChange(path) {
      // console.log('path:', path)
      // console.log('current path:', window.location.pathname)
      // console.log(path == window.location.pathname)
      this.menu = undefined
      if (path == window.location.pathname) {
        return
      }
      if (path) {
        this.$router.push({ path: path })
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.search {
  position: relative;
  top: -16px;
  display: inline-block;
  height: 100%;
  // background-color: yellow;
  // width: 150px;

  .input {
    width: 120px;
  }
}
</style>
<style>
.search .el-input__inner {
  border: none;
}
</style>
