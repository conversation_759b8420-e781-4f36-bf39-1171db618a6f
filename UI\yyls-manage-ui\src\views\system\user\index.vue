<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <!-- <el-form label-width="80px">
        <el-row :gutter="15">
          <el-col :span="8">
            <el-form-item label="标题">
              <el-input v-model="queryParams.title"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <el-button type="primary" icon="el-icon-search"  @click="$refs.table.refresh(true)">搜索</el-button>
              <el-button icon="el-icon-refresh"  @click="() => queryParams = {}">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form> -->

      <el-row ref="toolbar" class="table-toolbar">
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
          v-permission="['system:user:add']"
          >新增</el-button
        >
        <el-button
          size="mini"
          type="danger"
          icon="el-icon-plus"
          @click="handleDeleteAllUserLoginToken"
          v-permission="['system:user:add']"
          >清除所有登录用户Token</el-button
        >
      </el-row>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
      >
        <el-table-column type="index" label="序号" align="center" width="50"></el-table-column>
        <el-table-column prop="id" label="ID" align="center" width="80"></el-table-column>
        <!-- <el-table-column prop="authUId" label="用户中心ID" align="center" width="80"></el-table-column> -->
        <el-table-column
          prop="userName"
          label="登录账号"
          align="center"
          width="120"
        ></el-table-column>
        <el-table-column prop="nickName" label="姓名" align="center" width="120"></el-table-column>
        <el-table-column
          prop="authUId"
          label="授权中心ID"
          align="center"
          width="120"
        ></el-table-column>
        <!-- <el-table-column
          prop="department"
          label="部门"
          align="center"
          width="120"
        ></el-table-column> -->
        <!-- <el-table-column prop="phone" label="电话" align="center" width="120"></el-table-column>
        <el-table-column prop="email" label="邮箱" align="center" width="120"></el-table-column> -->
        <el-table-column prop="status" label="状态" align="center" width="100">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.status"
              :disabled="!changeStatusPermission"
              :active-value="true"
              :inactive-value="false"
              active-color="#13ce66"
              @change="onStatusChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" align="center"></el-table-column>
        <el-table-column prop="loginIp" label="最后登录IP" align="center" width="180">
          <template slot-scope="scope">
            <div>{{ scope.row.loginIp }}</div>
            <div>{{ scope.row.loginIpLocation }}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="loginTime"
          label="最后登录时间"
          align="center"
          width="180"
        ></el-table-column>
        <el-table-column
          prop="createOn"
          label="创建时间"
          align="center"
          width="180"
        ></el-table-column>
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding"
          fixed="right"
          width="350"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              v-permission="['system:user:resetPassword']"
              @click="handleResetPassword(scope.row)"
              >重置密码</el-button
            >
            <el-button
              size="mini"
              type="primary"
              v-permission="['system:user:dataPermission']"
              @click="handleDataPermission(scope.row)"
              >数据权限</el-button
            >
            <el-button
              size="mini"
              type="warning"
              v-permission="['system:user:edit']"
              @click="handleEdit(scope.row)"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="danger"
              v-permission="['system:user:delete']"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </x-table>
    </el-card>

    <edit-dialog ref="editDialog" @ok="handleOk" />
  </div>
</template>

<script>
import { tableHeightMixin } from '@/mixin'
import { sysUserApi } from '@/api'
import EditDialog from './Edit'

export default {
  mixins: [tableHeightMixin],
  components: {
    EditDialog,
  },
  data() {
    return {
      queryParams: {},
      loading: false,
      tableData: {},
      changeStatusPermission: false,
    }
  },
  mounted() {
    const changeStatusPermission = this.$hasPermission('system:user:changeStatus')
    this.changeStatusPermission = changeStatusPermission
  },
  methods: {
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      const res = await sysUserApi.getList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    handleAdd() {
      this.$refs.editDialog.add()
    },
    handleEdit(row) {
      this.$refs.editDialog.edit(row)
    },
    async handleDelete(row) {
      this.$confirm('是否确认删除该数据项？', '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          const res = await sysUserApi.deleteUser(row.id)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('删除成功')
          } else {
            this.$xMsgError('删除失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },

    async handleDeleteAllUserLoginToken() {
      this.$confirm('是否确认删除清除用户登录Token？', '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          const res = await sysUserApi.deleteAllUserLoginToken()
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('删除成功')
          } else {
            this.$xMsgError('删除失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },

    async handleResetPassword(row) {
      this.$confirm('是否确认重置该用户的密码', '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          const res = await sysUserApi.resetPassword(row.id)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('重置密码成功')
          } else {
            this.$xMsgError('操作失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },

    handleOk() {
      this.$refs.table.refresh()
    },

    onStatusChange(row) {
      let statusMsg = row.status == true ? '启用' : '禁用'
      this.$confirm(`是否确认【${statusMsg}】该账号？`, '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          const res = await sysUserApi.changeStatus(row.id, row.status)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$xMsgSuccess('操作成功')
          } else {
            this.$xMsgError('操作失败！' + res.msg)
          }
          this.$refs.table.refresh()
        })
        .catch(() => {
          this.$xloading.hide()
          this.$refs.table.refresh()
        })
    },
  },
}
</script>

<style></style>
