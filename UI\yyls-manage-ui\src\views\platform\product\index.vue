<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <el-form ref="queryForm" inline size="mini">
        <el-form-item label="平台">
          <x-select
            url="/options/getPlatformOptions"
            v-model="queryParams.platformId"
            showDefault
          ></x-select>
        </el-form-item>
        <el-form-item label="商品id">
          <el-input v-model="queryParams.productId"></el-input>
        </el-form-item>
        <el-form-item label="标题">
          <el-input v-model="queryParams.title"></el-input>
        </el-form-item>

        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!--<el-row ref="toolbar" class="table-toolbar">
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
          v-permission="['platformProduct:add']"
          >新增</el-button
        >
      </el-row>-->

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
      >
        <el-table-column type="index" label="序号" align="center" min-width="50"></el-table-column>
        <el-table-column label="商品图片" align="center" min-width="100">
          <template slot-scope="scope">
            <div
              class="image-carousel-container"
              v-if="scope.row.imageUrls && scope.row.imageUrls.length > 0"
            >
              <el-carousel
                height="80px"
                :autoplay="false"
                :interval="3000"
                indicator-position="none"
                arrow="hover"
              >
                <el-carousel-item
                  v-for="(image, index) in getImageArray(scope.row.imageUrls)"
                  :key="index"
                >
                  <img
                    :src="image"
                    :alt="`商品图片${index + 1}`"
                    class="carousel-image"
                    @error="handleImageError"
                    @click="previewImage(image)"
                  />
                </el-carousel-item>
              </el-carousel>
            </div>
            <span v-else class="no-image">暂无图片</span>
          </template>
        </el-table-column>
        <el-table-column prop="productId" label="商品信息" align="center" min-width="280">
          <template v-slot="{ row }">
            <div class="product-info">
              <div class="product-id" @click="hanlderDetail(row)" style="cursor: pointer">
                <span class="label">商品ID:</span>
                <span class="value">{{ row.productId }}</span>
              </div>
              <div class="product-title">
                <span class="label">商品名称:</span>
                <span class="value" :title="row.title">{{ row.title }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="平台" align="center" min-width="100">
          <template v-slot="{ row }">
            <platform-icon :platform="row.platformId"></platform-icon>
          </template>
        </el-table-column>
        <el-table-column prop="isRecommend" label="首页显示" align="center" min-width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.isRecommend ? 'success' : 'danger'">{{
              scope.row.isRecommend ? '是' : '否'
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="enable" label="是否启用" align="center" min-width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.enable ? 'success' : 'danger'">{{
              scope.row.enable ? '启用' : '停用'
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="salePrice" label="销售价格" align="center" min-width="100">
          <template slot-scope="scope">
            <span>¥{{ (scope.row.salePrice / 100).toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="commissionServiceRatio"
          label="佣金比例"
          align="center"
          min-width="100"
        >
          <template slot-scope="scope">
            <span>{{ (scope.row.commissionServiceRatio / 10000).toFixed(2) }}%</span>
          </template>
        </el-table-column>
        <el-table-column label="佣金金额" align="center" min-width="100">
          <template slot-scope="scope">
            <span class="commission-amount"
              >¥{{
                calculateCommission(scope.row.salePrice, scope.row.commissionServiceRatio)
              }}</span
            >
          </template>
        </el-table-column>
        <el-table-column
          prop="createTime"
          label="添加时间"
          align="center"
          min-width="150"
        ></el-table-column>
        <el-table-column
          prop="editTime"
          label="修改时间"
          align="center"
          min-width="150"
        ></el-table-column>
        <el-table-column label="操作" width="160" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="warning"
              @click="handleEdit(scope.row)"
              v-permission="['platformProduct:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
              v-permission="['platformProduct:delete']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </x-table>
    </el-card>
  </div>
</template>

<script>
import { PLATFORM } from '@/utils/constant'
import PlatformIcon from '@/components/Business/ShopPlatformIcon'
import { tableHeightMixin } from '@/mixin'
import { platformProductApi } from '@/api'
export default {
  components: {
    PlatformIcon,
  },
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {},
      loading: false,
      tableData: {},
      WXLeague: PLATFORM.WXLeague,
    }
  },
  methods: {
    queryReset() {
      this.queryParams = {}
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      const res = await platformProductApi.getList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    handleAdd() {
      //this.$refs.editDialog.add()
    },
    handleEdit(row) {
      //this.$refs.editDialog.edit(row)
      this.$router.push({ path: '/product/choseproduct/edit', query: { id: row.id } })
    },
    hanlderDetail(row) {
      let extraData = JSON.parse(row.extraData)
      if (row.platformId === this.WXLeague) {
        this.$router.push({
          path: '/product/wxlproduct/detail',
          query: {
            id: row.productId,
            shopAppid: extraData.ShopAppid,
            planType: extraData.PlanType,
            headSupplierAppid: extraData.HeadSupplierAppid,
          },
        })
      }
    },
    async handleDelete(row) {
      this.$confirm('是否确认删除该数据项？', '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          const res = await platformProductApi.delPlatformProduct(row.id)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('删除成功')
          } else {
            this.$xMsgError('删除失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },

    getImageArray(imageUrls) {
      if (!imageUrls) return []
      if (typeof imageUrls === 'string') {
        try {
          return JSON.parse(imageUrls)
        } catch (e) {
          // 如果不是JSON格式，按逗号分割
          return imageUrls.split(',').filter((url) => url.trim())
        }
      }
      return Array.isArray(imageUrls) ? imageUrls : []
    },

    handleImageError(event) {
      event.target.src = 'https://via.placeholder.com/80x80?text=No+Image'
    },

    previewImage(imageUrl) {
      this.$alert(`<img src="${imageUrl}" style="width: 100%; max-width: 500px;"/>`, '图片预览', {
        dangerouslyUseHTMLString: true,
        showConfirmButton: false,
        showClose: true,
      })
    },

    // 计算佣金金额
    calculateCommission(salePrice, commissionServiceRatio) {
      if (!salePrice || !commissionServiceRatio) return '0.00'
      const commission = (salePrice * commissionServiceRatio) / 1000000
      return (commission / 100).toFixed(2)
    },
  },
}
</script>

<style scoped>
.image-carousel-container {
  width: 100%;
  height: 80px;
}

.carousel-image {
  width: 100%;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.2s;
}

.carousel-image:hover {
  transform: scale(1.05);
}

.no-image {
  color: #909399;
  font-size: 12px;
}

/* 自定义轮播组件样式 */
.image-carousel-container .el-carousel__container {
  height: 80px;
}

.image-carousel-container .el-carousel__item {
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-carousel-container .el-carousel__arrow {
  background-color: rgba(0, 0, 0, 0.5);
  border: none;
  width: 20px;
  height: 20px;
}

.image-carousel-container .el-carousel__arrow i {
  font-size: 12px;
}

/* 商品信息样式 */
.product-info {
  text-align: left;
  line-height: 1.6;
}

.product-info .product-id,
.product-info .product-title {
  margin-bottom: 8px;
  display: flex;
  align-items: flex-start;
}

.product-info .product-title {
  margin-bottom: 0;
}

.product-info .label {
  color: #606266;
  font-weight: 500;
  min-width: 70px;
  flex-shrink: 0;
  font-size: 13px;
}

.product-info .value {
  color: #303133;
  font-size: 13px;
  word-break: break-all;
  line-height: 1.4;
}

.product-info .product-id .value {
  font-family: 'Courier New', monospace;
  color: #409eff;
  font-weight: 500;
}

.product-info .product-title .value {
  max-width: 280px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 佣金金额样式 */
.commission-amount {
  color: #e6a23c;
  font-weight: 600;
  font-size: 14px;
}
</style>
