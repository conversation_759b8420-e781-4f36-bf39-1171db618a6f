<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <el-form ref="queryForm" inline label-width="80px">
        <el-row :gutter="15">
          <el-form-item label="登录账号">
            <el-input v-model="queryParams.loginName"></el-input>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="queryParams.status" placeholder="">
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="日期">
            <el-date-picker
              v-model="queryParams.date"
              type="daterange"
              placeholder=""
              value-format="yyyy-MM-dd"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="$refs.table.refresh(true)"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" @click="() => (queryParams = {})">重置</el-button>
          </el-form-item>
        </el-row>
      </el-form>

      <el-row ref="toolbar" class="table-toolbar"> </el-row>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
      >
        <el-table-column type="index" label="序号" align="center"></el-table-column>
        <el-table-column prop="logId" label="ID" align="center" width="120"></el-table-column>
        <el-table-column
          prop="loginName"
          label="登录账号"
          align="center"
          width="150"
        ></el-table-column>
        <el-table-column prop="realName" label="姓名" align="center" width="150"></el-table-column>
        <el-table-column prop="ipAddr" label="登录IP" align="center">
          <template slot-scope="scope">
            <div>{{ scope.row.ipAddr }}</div>
            <div>{{ scope.row.ipLocation }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" align="center">
          <template slot-scope="scope">
            <el-tag type="success" v-if="scope.row.status == '0'">成功</el-tag>
            <el-tag type="warning" v-else-if="scope.row.status == '1'">失败</el-tag>
            <el-tag type="danger" v-else>异常</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="msg" label="操作信息" align="center"></el-table-column>
        <el-table-column
          prop="loginTime"
          label="登录时间"
          align="center"
          width="180"
        ></el-table-column>
        <!-- <el-table-column label="操作" width="180" align="center">
          <template slot-scope="scope">
            <el-button size="mini" 
              @click="handleView(scope.row)"
            >详细</el-button>
          </template>
        </el-table-column> -->
      </x-table>
    </el-card>
  </div>
</template>

<script>
import { tableHeightMixin } from '@/mixin'
import * as logApi from '@/api/system/log'
export default {
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {},
      loading: false,
      tableData: {},
      statusOptions: [
        { label: '成功', value: '0' },
        { label: '失败', value: '1' },
      ],
    }
  },
  methods: {
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      if (params.date) {
        params.beginTime = params.date[0]
        params.endTime = params.date[1]
      }
      const res = await logApi.getLoginLog(params)
      this.tableData = res
      this.$xloading.hide()
    },

    renderOperateType(row) {
      const operateType = row.operateType
      let text = '未知'
      switch (operateType) {
        case '1':
          text = '新增'
          break
        case '2':
          text = '修改'
          break
        case '3':
          text = '删除'
          break
        case '4':
          text = '查询'
          break
        case '5':
          text = '退出登录'
          break
        default:
      }

      return text
    },
  },
}
</script>

<style></style>
