<template>
  <editor id="tinymce" v-model="content" class="editor" :init="editorInit" />
</template>

<script>
// eslint-disable-next-line no-unused-vars
import tinymce from 'tinymce/tinymce'
import Editor from '@tinymce/tinymce-vue'
import 'tinymce/themes/silver'
import 'tinymce/icons/default/icons.min.js'

import 'tinymce/plugins/code'
import 'tinymce/plugins/link'
import 'tinymce/plugins/anchor'
import 'tinymce/plugins/lists'
import 'tinymce/plugins/table'
import 'tinymce/plugins/image'
import 'tinymce/plugins/charmap'
import 'tinymce/plugins/insertdatetime'
import 'tinymce/plugins/preview'
import 'tinymce/plugins/fullpage'
import 'tinymce/plugins/emoticons'

import toolbar from './toolbar'
import plugins from './plugins'
import { uploadUrl } from '@/api/file'
import { getToken } from '@/utils/auth'

export default {
  components: {
    Editor,
  },
  props: {
    value: {
      type: String,
    },
    plugins: {
      type: [String, Array],
      default: () => {
        return plugins
      },
    },
    toolbar: {
      type: [String, Array],
      default: () => {
        return toolbar
      },
    },
    uploadImageType: {
      // 上传图片类型
      type: String,
    },
    uploadVideoType: {
      // 上传视频类型
      type: String,
    },
    uploadTempId: {
      type: String,
    },
    uploadTemp: {
      type: String,
    },
    height: {
      type: Number,
      default: 800,
    },
    fullpage: {
      type: Boolean,
      default: true,
    },
  },
  mounted() {
    // console.log(this.value)
  },
  data() {
    return {
      content: this.value,
      editorInit: {
        skin_url: '/static/tinymce/skins/ui/oxide',
        //content_css: '/static/tinymce/skins/content/default/content.css',
        language_url: '/static/tinymce/langs/zh_CN.js', // 语言包的路径
        language: 'zh_CN', // 语言
        height: this.height, // 编辑器高度
        branding: false, // 是否禁用“Powered by TinyMCE”
        menubar: false, // 顶部菜单栏显示
        // toolbar: 'undo redo | styleselect bold italic | alignleft aligncenter alignright bullist numlist outdent indent | forecolor backcolor',
        toolbar: this.toolbar,
        plugins: this.fullpage ? [...this.plugins, 'fullpage'] : this.plugins,
        images_upload_url: uploadUrl,
        image_uploadtab: true,
        convert_fonts_to_spans: false, // 不转换字体
        valid_elements: '*[*]', // 允许所有 HTML 标签和属性
        extended_valid_elements:
          'html,head,meta[*],link[*],title,style[*],script[*],body,font[*],a[*]',
        valid_children:
          '+html[head|body],+head[title|style|script|meta|link],+body[style|script|font|a]', // 确保完整结构
        forced_root_block: '', // 不强制使用 <p> 作为根标签
        custom_elements: 'html,head,meta,link,title,style,script,body,font', // 让 TinyMCE 识别这些标签
        content_css: false, // 不移除 style
        allow_html_in_named_anchor: true, // 允许 HTML 结构
        verify_html: false, // 关闭 HTML 过滤
        convert_urls: false, // 防止 URL 转换
        entity_encoding: 'raw', // 允许特殊 HTML 字符
        // 图片上传自定义实现
        images_upload_handler: (blobInfo, success, failure) => {
          var xhr, formData

          xhr = new XMLHttpRequest()
          xhr.withCredentials = false
          xhr.open('POST', uploadUrl)
          xhr.setRequestHeader('Access-Token', getToken())

          xhr.onload = function () {
            var json
            if (xhr.status != 200) {
              failure('HTTP Error: ' + xhr.status)
              return
            }
            json = JSON.parse(xhr.responseText)
            console.log(json)
            if (!json || json.code != 0) {
              failure('上传失败: ' + xhr.responseText)
              return
            }
            success(json.data.url)
          }
          formData = new FormData()
          formData.append('uploadType', this.uploadType)
          formData.append('uploadTemp', this.uploadTemp)
          formData.append('uploadTempId', this.uploadTempId)
          formData.append('file', blobInfo.blob(), blobInfo.filename())
          xhr.send(formData)
        },
        setup: (editor) => {
          editor.ui.registry.addButton('uploadVideoButton', {
            text: '上传视频',
            icon: 'new-document',
            onAction: () => {
              let input = document.createElement('input')
              input.type = 'file'
              input.accept = '.mp4'
              input.onchange = (event) => {
                let file = event.target.files[0]
                if (file) {
                  this.uploadVideo(file, editor) // 调用 Vue 组件内的方法
                }
              }
              input.click()
            },
          })
        },
      },
    }
  },
  watch: {
    value(newVal) {
      // console.log('value: ', newVal)
      this.content = newVal
    },
    content(newVal) {
      this.$emit('input', newVal)
    },
  },
  methods: {
    uploadCss(file, editor) {
      let formData = new FormData()
      formData.append('uploadType', this.uploadVideoType)
      formData.append('uploadTemp', this.uploadTemp)
      formData.append('uploadTempId', this.uploadTempId)
      formData.append('file', file)

      // 发送到后端 API 进行上传
      fetch(uploadUrl, {
        method: 'POST',
        headers: {
          'Access-Token': getToken(), // 需要 token 认证
        },
        body: formData,
      })
        .then((response) => response.json())
        .then((data) => {
          if (data.code === 0 && data.data.url) {
            let videoUrl = data.data.url

            // --- 核心修改在这里 ---

            // 1. 创建视频的 HTML 标签
            // - src: 视频地址
            // - controls: 显示播放、暂停、音量等控件，这对于视频几乎是必须的
            // - width="100%": 让视频宽度自适应编辑器容器
            // - <p>...</p>: 为不支持 <video> 标签的旧版浏览器提供提示
            const videoHtml = `
          <p>
            <video src="${videoUrl}" controls width="100%">
              您的浏览器不支持 HTML5 video 标签。
            </video>
          </p>
        `
            // 2. 使用编辑器的 API 插入内容 (这是最佳实践！)
            //    大多数富文本编辑器（如 TinyMCE, CKEditor）都提供 insertContent 方法，
            //    它可以在当前光标位置插入内容，而不是粗暴地替换所有内容。
            if (editor && typeof editor.insertContent === 'function') {
              editor.insertContent(videoHtml)
            } else {
              // 如果编辑器没有 insertContent 方法，则使用备用方案：在末尾追加
              this.content = editor.getContent() + videoHtml
              editor.setContent(this.content)
            }

            this.$xMsgSuccess('上传视频成功')
          }
        })
        .catch((error) => {
          //console.error('视频上传失败:', error)
          this.$xMsgError('上传视频失败：' + error)
        })
    },
  },
}
</script>

<style></style>
