<template>
  <x-dialog
    :title="title"
    :visible.sync="visible"
    v-loading="loading"
    @submit="submitForm"
    @cancel="visible = false"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px" size="medium">
      <el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="登录账号" prop="userName">
              <el-input v-model="form.userName" placeholder="" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="!isEdit">
            <el-form-item label="密码" prop="password">
              <el-input
                v-model="form.password"
                placeholder=""
                show-password
                autocomplete="new-password"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-col :span="12">
          <el-form-item label="姓名" prop="nickName">
            <el-input v-model="form.nickName" placeholder="" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="用户中心ID" prop="authUId">
            <el-input v-model="form.authUId" placeholder="" />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="12">
          <el-form-item label="部门" prop="department">
            <el-input v-model="form.department" placeholder="" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="电话" prop="phone">
            <el-input v-model="form.phone" placeholder="" />
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="form.email" placeholder="" />
          </el-form-item>
        </el-col> 
        <el-col :span="12">
          <el-form-item label="系统权限" prop="systemCodes">
            <x-checkbox v-model="form.systemCodes" :options="systemOptions"></x-checkbox>
          </el-form-item>
        </el-col>-->
        <el-col :span="24">
          <el-form-item label="角色" prop="roleIds">
            <x-checkbox
              v-model="form.roleIds"
              url="/system/user/roleList"
              :map="roleOptionsMap"
            ></x-checkbox>
            <!-- <el-checkbox-group v-model="form.roleIds">
              <el-checkbox v-for="item in roleOptions" :key="item.value" :label="item.value">{{ item.label }}</el-checkbox>
            </el-checkbox-group> -->
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio :label="true">启用</el-radio>
              <el-radio :label="false">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="24">
          <el-form-item label="性别" prop="sex" >
            <el-radio-group v-model="form.sex">
              <el-radio v-for="item in sexOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col> -->

        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input type="textarea" v-model="form.remark" placeholder="" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </x-dialog>
</template>

<script>
import { sysUserApi } from '@/api'

export default {
  data() {
    return {
      isEdit: false,
      title: '',
      visible: false,
      loading: false,
      form: {},
      rules: {
        userName: [{ required: true, message: '登录账号不能为空', trigger: 'blur' }],
        password: [
          { required: true, message: '密码不能为空', trigger: 'blur' },
          { min: 6, max: 16, message: '长度在 6 到 16 个字符', trigger: 'blur' },
        ],
        nickName: [{ required: true, message: '姓名不能为空', trigger: 'blur' }],
        roleIds: [{ required: true, message: '角色不能为空', trigger: 'blur' }],
        systemCodes: [{ required: true, message: '系统权限不能为空', trigger: 'blur' }],
        status: [{ required: true, message: '状态不能为空', trigger: 'blur' }],
      },
      roleOptions: [],
    }
  },
  async created() {},
  methods: {
    add() {
      this.isEdit = false
      this.reset()
      this.title = '新增'
      this.visible = true
      this.getRoleOptions()
    },
    async edit(row) {
      this.isEdit = true
      this.reset()
      this.title = '修改'
      this.visible = true
      this.$xloading.show()
      await this.getRoleOptions()
      const res = await sysUserApi.getDetail(row.id)
      if (res.code == 0) {
        this.$xloading.hide()
        this.form = res.data
        const { authUId } = res.data
        if (authUId == 0) {
          this.form.authUId = undefined
        }
        // const roleIds = res.data.roleIds.map(t => {
        //   return t.id
        // })
        // this.$set(this.form, 'roleIds', roleIds)
      }
    },

    reset() {
      this.form = {
        id: undefined,
        title: undefined,
        roleIds: [],
        systemCodes: [],
      }
      this.$xResetForm('form')
    },

    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        // console.log('submitForm valid: ', valid, 'data: ', this.form)
        if (valid) {
          this.$xloading.show()
          const data = Object.assign({}, this.form)
          const { id } = data
          if (id != undefined) {
            const res = await sysUserApi.editUser(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('修改成功')
              this.visible = false
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          } else {
            const res = await sysUserApi.addUser(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('添加成功')
              this.visible = false
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          }
        }
      })
    },

    roleOptionsMap(options) {
      return options.map((t) => {
        return {
          label: t.roleName,
          value: t.id,
        }
      })
    },

    async getRoleOptions() {
      const res = await sysUserApi.getRoleList()
      if (res.code == 0) {
        this.roleOptions = res.data.map((t) => {
          return {
            label: t.roleName,
            value: t.id,
          }
        })
      }
    },
  },
}
</script>

<style></style>
