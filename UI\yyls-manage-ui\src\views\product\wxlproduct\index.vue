<template>
  <div class="app-container">
    <el-card>
      <!-- 搜索表单 -->
      <el-form ref="queryForm" :model="queryParams" inline size="mini">
        <el-form-item label="计划类型">
          <el-select v-model="queryParams.plan_type" placeholder="请选择计划类型" clearable>
            <el-option label="定向计划" :value="1"></el-option>
            <el-option label="公开计划" :value="2"></el-option>
            <el-option label="机构定向计划" :value="3"></el-option>
            <el-option label="机构普通计划" :value="4"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="搜索关键词">
          <el-input v-model="queryParams.keyword" placeholder="请输入商品名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="店铺APPID">
          <el-input
            v-model="queryParams.shop_appid"
            placeholder="请输入店铺APPID"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="售卖价区间">
          <inpute-area :range="queryParams.selling_price_range"></inpute-area>
        </el-form-item>
        <el-form-item label="月销量区间">
          <inpute-area :range="queryParams.monthly_sales_range" unit="件"></inpute-area>
        </el-form-item>
        <!--<el-form-item label="佣金率区间">
          <inpute-area :range="queryParams.commission_rate_range" unit="%"></inpute-area>
        </el-form-item>-->
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="handleSearch">
            搜索
          </el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="handleReset"> 重置 </el-button>
        </el-form-item>
      </el-form>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 商品分类标签页 -->
        <el-tabs v-model="selectedCategoryId" @tab-click="handleTabClick" class="category-tabs">
          <!-- 全部分类标签页 -->
          <el-tab-pane label="全部" name="">
            <!-- 商品列表 -->
            <div v-loading="loading" class="product-list">
              <!-- 选品操作栏 -->
              <div v-if="productList.length > 0" class="selection-toolbar">
                <div class="selection-controls">
                  <el-checkbox
                    v-model="selectAll"
                    :indeterminate="isIndeterminate"
                    @change="handleSelectAll"
                  >
                    全选
                  </el-checkbox>
                  <span class="selected-count">已选择 {{ selectedProducts.length }} 个商品</span>
                </div>
                <el-button
                  type="primary"
                  size="small"
                  :disabled="selectedProducts.length === 0"
                  @click="handleSelectProducts"
                >
                  选品 ({{ selectedProducts.length }})
                </el-button>
              </div>

              <div v-if="productList.length === 0 && !loading" class="empty-state">
                <div class="custom-empty">
                  <div class="empty-icon">📦</div>
                  <div class="empty-text">暂无商品数据</div>
                </div>
              </div>
              <div v-else class="product-grid">
                <div
                  v-for="product in productList"
                  :key="product.product_id || Math.random()"
                  class="product-card"
                  :class="{
                    selected: isProductSelected(product),
                    'already-chosen': isProductAlreadyChosen(product),
                  }"
                >
                  <!-- 商品选择框 -->
                  <div class="product-checkbox">
                    <el-checkbox
                      :value="isProductSelected(product)"
                      :disabled="isProductAlreadyChosen(product)"
                      @change="handleProductSelect(product, $event)"
                      @click.stop
                    ></el-checkbox>
                  </div>
                  <!-- 已选品标识 -->
                  <div v-if="isProductAlreadyChosen(product)" class="chosen-badge">
                    <el-tag type="success" size="mini">已选品</el-tag>
                  </div>
                  <!-- 商品图片轮播 -->
                  <div class="product-image" @click="handleProductDetail(product)">
                    <el-carousel
                      v-if="getProductImages(product).length > 1"
                      height="200px"
                      :autoplay="false"
                      indicator-position="outside"
                    >
                      <el-carousel-item
                        v-for="(image, index) in getProductImages(product)"
                        :key="index"
                      >
                        <img
                          :src="image"
                          :alt="getProductTitle(product)"
                          @error="handleImageError"
                        />
                      </el-carousel-item>
                    </el-carousel>
                    <img
                      v-else
                      :src="getProductImage(product)"
                      :alt="getProductTitle(product)"
                      @error="handleImageError"
                    />
                  </div>

                  <!-- 商品信息 -->
                  <div class="product-info" @click="handleProductDetail(product)">
                    <h3 class="product-title">{{ getProductTitle(product) }}</h3>
                    <p class="product-subtitle" v-if="getProductSubTitle(product)">
                      {{ getProductSubTitle(product) }}
                    </p>

                    <!-- 价格信息 -->
                    <div class="price-info">
                      <span class="current-price">¥{{ formatPrice(getMinPrice(product)) }}</span>
                    </div>

                    <!-- 佣金信息 -->
                    <div class="commission-info" v-if="getCommissionRatio(product) > 0">
                      <el-tag type="success" size="mini">
                        佣金: {{ getCommissionRatio(product) }}%
                      </el-tag>
                      <el-tag type="warning" size="mini" style="margin-left: 5px">
                        ¥{{ getCommissionAmount(product) }}
                      </el-tag>
                    </div>

                    <!-- 商品ID信息 -->
                    <div class="product-id-info">
                      <span class="product-id">商品ID: {{ getProductId(product) }}</span>
                    </div>

                    <!-- 店铺信息 -->
                    <div class="shop-info" v-if="getShopInfo(product)">
                      <span class="shop-name">商家店铺ID: {{ getShopInfo(product) }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 分页 -->
              <div class="pagination-wrapper" v-if="productList.length > 0">
                <el-button
                  v-if="hasMore"
                  type="primary"
                  :loading="loading"
                  @click="loadMore"
                  style="width: 30%; margin-top: 20px"
                >
                  加载更多
                </el-button>
                <div v-else class="no-more-data">
                  <span>已加载全部数据</span>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <!-- 其他分类标签页 -->
          <el-tab-pane
            v-for="category in categoryList"
            :key="category.value"
            :label="category.label"
            :name="category.value"
          >
            <!-- 商品列表 -->
            <div v-loading="loading" class="product-list">
              <!-- 选品操作栏 -->
              <div v-if="productList.length > 0" class="selection-toolbar">
                <div class="selection-controls">
                  <el-checkbox
                    v-model="selectAll"
                    :indeterminate="isIndeterminate"
                    @change="handleSelectAll"
                  >
                    全选
                  </el-checkbox>
                  <span class="selected-count">已选择 {{ selectedProducts.length }} 个商品</span>
                </div>
                <el-button
                  type="primary"
                  size="small"
                  :disabled="selectedProducts.length === 0"
                  @click="handleSelectProducts"
                >
                  选品 ({{ selectedProducts.length }})
                </el-button>
              </div>

              <div v-if="productList.length === 0 && !loading" class="empty-state">
                <div class="custom-empty">
                  <div class="empty-icon">📦</div>
                  <div class="empty-text">暂无商品数据</div>
                </div>
              </div>
              <div v-else class="product-grid">
                <div
                  v-for="product in productList"
                  :key="product.product_id || Math.random()"
                  class="product-card"
                  :class="{
                    selected: isProductSelected(product),
                    'already-chosen': isProductAlreadyChosen(product),
                  }"
                >
                  <!-- 商品选择框 -->
                  <div class="product-checkbox">
                    <el-checkbox
                      :value="isProductSelected(product)"
                      :disabled="isProductAlreadyChosen(product)"
                      @change="handleProductSelect(product, $event)"
                      @click.stop
                    ></el-checkbox>
                  </div>
                  <!-- 已选品标识 -->
                  <div v-if="isProductAlreadyChosen(product)" class="chosen-badge">
                    <el-tag type="success" size="mini">已选品</el-tag>
                  </div>
                  <!-- 商品图片轮播 -->
                  <div class="product-image" @click="handleProductDetail(product)">
                    <el-carousel
                      v-if="getProductImages(product).length > 1"
                      height="200px"
                      :autoplay="false"
                      indicator-position="outside"
                    >
                      <el-carousel-item
                        v-for="(image, index) in getProductImages(product)"
                        :key="index"
                      >
                        <img
                          :src="image"
                          :alt="getProductTitle(product)"
                          @error="handleImageError"
                        />
                      </el-carousel-item>
                    </el-carousel>
                    <img
                      v-else
                      :src="getProductImage(product)"
                      :alt="getProductTitle(product)"
                      @error="handleImageError"
                    />
                  </div>

                  <!-- 商品信息 -->
                  <div class="product-info" @click="handleProductDetail(product)">
                    <h3 class="product-title">{{ getProductTitle(product) }}</h3>
                    <p class="product-subtitle" v-if="getProductSubTitle(product)">
                      {{ getProductSubTitle(product) }}
                    </p>

                    <!-- 价格信息 -->
                    <div class="price-info">
                      <span class="current-price">¥{{ formatPrice(getMinPrice(product)) }}</span>
                    </div>

                    <!-- 佣金信息 -->
                    <div class="commission-info" v-if="getCommissionRatio(product) > 0">
                      <el-tag type="success" size="mini">
                        佣金: {{ getCommissionRatio(product) }}%
                      </el-tag>
                      <el-tag type="warning" size="mini" style="margin-left: 5px">
                        ¥{{ getCommissionAmount(product) }}
                      </el-tag>
                    </div>

                    <!-- 商品ID信息 -->
                    <div class="product-id-info">
                      <span class="product-id">商品ID: {{ getProductId(product) }}</span>
                    </div>

                    <!-- 店铺信息 -->
                    <div class="shop-info" v-if="getShopInfo(product)">
                      <span class="shop-name">商家店铺ID: {{ getShopInfo(product) }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 分页 -->
              <div class="pagination-wrapper" v-if="productList.length > 0">
                <el-button
                  v-if="hasMore"
                  type="primary"
                  :loading="loading"
                  @click="loadMore"
                  style="width: 30%; margin-top: 20px"
                >
                  加载更多
                </el-button>
                <div v-else class="no-more-data">
                  <span>已加载全部数据</span>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>
  </div>
</template>

<script>
import { wxlProductApi } from '@/api/index'
import { getPlatformCategoryOptions } from '@/utils/options'
import InputeArea from '@/components/Form/InputeArea'

export default {
  name: 'WXLProductList',
  components: {
    InputeArea,
  },
  data() {
    return {
      loading: false,
      productList: [],
      categoryList: [],
      selectedCategoryId: '',
      queryParams: {
        plan_type: 2,
        keyword: '',
        shop_appid: '',
        page_size: 8,
        next_key: '',
        spu_item_condition: {
          selling_price_range: null,
          monthly_sales_range: null,
          commission_rate_range: null,
        },
        selling_price_range: {
          min: 0,
          max: 100000,
        },
        monthly_sales_range: {
          min: 0,
          max: 100000,
        },
        commission_rate_range: {
          min: 0,
          max: 100,
        },
      },
      hasMore: true,
      selectedProducts: [], // 选中的商品列表
      selectAll: false, // 全选状态
    }
  },
  computed: {
    // 是否为半选状态
    isIndeterminate() {
      const selectedCount = this.selectedProducts.length
      const totalCount = this.productList.length
      return selectedCount > 0 && selectedCount < totalCount
    },
  },
  async mounted() {
    await this.getCategoryList()
    this.getProductList()
  },
  methods: {
    async getProductList(isLoadMore = false) {
      if (this.loading) return

      this.loading = true
      try {
        const params = { ...this.queryParams }
        if (this.selectedCategoryId) {
          params.category = {
            category_id: this.selectedCategoryId,
          }
        }
        if (params.selling_price_range) {
          params.spu_item_condition.selling_price_range = {
            min: 0,
            max: 0,
          }
          params.spu_item_condition.selling_price_range.min = params.selling_price_range.min * 100
          params.spu_item_condition.selling_price_range.max = params.selling_price_range.max * 100
        }
        if (params.monthly_sales_range) {
          params.spu_item_condition.monthly_sales_range = {
            min: 0,
            max: 0,
          }
          params.spu_item_condition.monthly_sales_range.min = params.monthly_sales_range.min
          params.spu_item_condition.monthly_sales_range.max = params.monthly_sales_range.max
        }
        if (params.commission_rate_range) {
          params.spu_item_condition.commission_rate_range = {
            min: 0,
            max: 0,
          }
          params.spu_item_condition.commission_rate_range.min =
            params.commission_rate_range.min * 1000
          params.spu_item_condition.commission_rate_range.max =
            params.commission_rate_range.max * 1000
        }
        const response = await wxlProductApi.getList(params)

        if (response.code === 0 && response.data) {
          if (isLoadMore) {
            this.productList = [...this.productList, ...response.data]
          } else {
            this.productList = response.data
            // 重新加载时清空选择状态
            this.selectedProducts = []
            this.selectAll = false
          }
          this.hasMore = response.data.length >= this.queryParams.page_size
          this.queryParams.next_key = response.data[response.data.length - 1].next_key
        } else {
          this.$message.error(response.msg || '获取商品列表失败')
        }
      } catch (error) {
        console.error('获取商品列表失败:', error)
        this.$message.error('获取商品列表失败')
      } finally {
        this.loading = false
      }
    },

    handleSearch() {
      this.queryParams.next_key = ''
      this.hasMore = true
      this.getProductList()
    },

    handleReset() {
      this.queryParams = {
        plan_type: 2,
        keyword: '',
        shop_appid: '',
        page_size: 8,
        next_key: '',
      }
      this.selectedCategoryId = ''
      this.hasMore = true
      this.getProductList()
    },

    loadMore() {
      this.getProductList(true)
    },

    handleProductDetail(product) {
      const productId = this.getProductId(product)
      const shopAppid = this.getShopInfo(product)
      const planType = this.queryParams.plan_type
      const headSupplierAppid = this.getHeadSupplierAppid(product)

      if (!productId || !shopAppid || !planType) {
        this.$message.error('缺少必要参数，无法查看详情')
        return
      }

      window.open(
        `/product/wxlproduct/detail?id=${productId}&shopAppid=${shopAppid}&planType=${planType}&headSupplierAppid=${headSupplierAppid}`
      )

      /*this.$router.push({
        path: '/product/wxlproduct/detail',
        query: {
          id: productId,
          shopAppid: shopAppid,
          planType: planType,
          headSupplierAppid: headSupplierAppid,
        },
      })*/
    },

    getProductImage(product) {
      const images =
        (product &&
          product.product &&
          product.product.productInfo &&
          product.product.productInfo.headImgs) ||
        []
      return images.length > 0 ? images[0].trim() : '/static/images/no-image.png'
    },

    // 获取所有商品图片
    getProductImages(product) {
      const images =
        (product &&
          product.product &&
          product.product.productInfo &&
          product.product.productInfo.headImgs) ||
        []
      return images.length > 0 ? images : ['/static/images/no-image.png']
    },

    // 获取商品ID
    getProductId(product) {
      return (product && product.product && product.product.productId) || ''
    },

    getProductTitle(product) {
      return (
        (product &&
          product.product &&
          product.product.productInfo &&
          product.product.productInfo.title) ||
        '暂无标题'
      )
    },

    getProductSubTitle(product) {
      return (
        (product &&
          product.product &&
          product.product.productInfo &&
          product.product.productInfo.subTitle) ||
        ''
      )
    },

    getMinPrice(product) {
      const skus =
        (product &&
          product.product &&
          product.product.productInfo &&
          product.product.productInfo.skus) ||
        []
      if (skus.length === 0) return 0
      return Math.min(...skus.map((sku) => sku.salePrice || 0))
    },

    getCommissionRatio(product) {
      const commissionInfo = product && product.product && product.product.commissionInfo
      if (commissionInfo && commissionInfo.serviceRatio) {
        return (commissionInfo.serviceRatio / 10000).toFixed(2)
      }
      return '0.00'
    },

    getCommissionAmount(product) {
      const minPrice = this.formatPrice(this.getMinPrice(product))
      const commissionRatio = this.getCommissionRatio(product)
      if (minPrice > 0 && commissionRatio > 0) {
        return ((minPrice * commissionRatio) / 100).toFixed(2)
      }
      return '0.00'
    },

    getShopInfo(product) {
      return (product && product.product && product.product.shopAppid) || ''
    },

    getHeadSupplierAppid(product) {
      return (
        (product &&
          product.product &&
          product.product.productInfo &&
          product.product.productInfo.headSupplierInfo &&
          product.product.productInfo.headSupplierInfo.headSupplierAppid) ||
        ''
      )
    },

    formatPrice(price) {
      if (!price || isNaN(price)) return '0.00'
      return (price / 100).toFixed(2)
    },

    handleImageError(event) {
      event.target.src = '/static/images/no-image.png'
    },

    async getCategoryList() {
      try {
        const categories = await getPlatformCategoryOptions({ level: 1 })
        console.log(categories)
        this.categoryList = categories || []
      } catch (error) {
        console.error('获取类别列表失败:', error)
        this.$message.error('获取类别列表失败')
      }
    },

    handleCategorySelect(categoryId) {
      this.selectedCategoryId = categoryId
      this.queryParams.next_key = ''
      this.hasMore = true
      this.selectedProducts = [] // 切换分类时清空选择
      this.selectAll = false
      this.getProductList()
    },

    // 处理 el-tabs 标签页切换
    handleTabClick(tab) {
      const categoryId = tab.name
      this.handleCategorySelect(categoryId)
    },

    // 判断商品是否已经选品
    isProductAlreadyChosen(product) {
      return product && product.isChose === true
    },

    // 判断商品是否被选中
    isProductSelected(product) {
      const productId = this.getProductId(product)
      return this.selectedProducts.some((p) => this.getProductId(p) === productId)
    },

    // 处理单个商品选择
    handleProductSelect(product, checked) {
      // 如果商品已经选品，则不允许操作
      if (this.isProductAlreadyChosen(product)) {
        return
      }

      const productId = this.getProductId(product)
      if (checked) {
        // 添加到选中列表
        if (!this.isProductSelected(product)) {
          this.selectedProducts.push(product)
        }
      } else {
        // 从选中列表移除
        this.selectedProducts = this.selectedProducts.filter(
          (p) => this.getProductId(p) !== productId
        )
      }

      // 更新全选状态
      this.updateSelectAllState()
    },

    // 处理全选
    handleSelectAll(checked) {
      if (checked) {
        // 只选择未选品的商品
        this.selectedProducts = this.productList.filter(
          (product) => !this.isProductAlreadyChosen(product)
        )
      } else {
        this.selectedProducts = []
      }
      this.selectAll = checked
    },

    // 更新全选状态
    updateSelectAllState() {
      const selectedCount = this.selectedProducts.length
      // 只计算未选品的商品数量
      const availableCount = this.productList.filter(
        (product) => !this.isProductAlreadyChosen(product)
      ).length
      this.selectAll = selectedCount === availableCount && availableCount > 0
    },

    // 处理选品操作
    async handleSelectProducts() {
      if (this.selectedProducts.length === 0) {
        this.$message.warning('请先选择商品')
        return
      }

      let successCount = 0
      let failCount = 0
      let failMessages = []

      await this.$confirm('确认添加选品吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          for (const product of this.selectedProducts) {
            let data = {
              productId: this.getProductId(product),
              shopAppid: this.getShopInfo(product),
              planType: this.queryParams.plan_type,
              headSupplierAppid: this.getHeadSupplierAppid(product),
            }
            let res = await wxlProductApi.choseProduct(data)
            if (res && res.code == 0) {
              successCount++
              // 选品成功后设置isChose为true
              product.isChose = true
            } else {
              failCount++
              failMessages.push(res.msg || '未知错误')
            }
          }
          this.$xloading.hide()
          // 统一输出结果
          if (successCount > 0 && failCount === 0) {
            this.$message.success(`全部选品成功！共处理 ${successCount} 个商品`)
          } else if (successCount === 0 && failCount > 0) {
            this.$message.error(`全部选品失败！共 ${failCount} 个商品失败`)
          } else if (successCount > 0 && failCount > 0) {
            this.$message.warning(`选品完成！成功 ${successCount} 个，失败 ${failCount} 个`)
          }
          // 如果有失败的商品，显示详细错误信息
          if (failCount > 0) {
            const uniqueErrors = [...new Set(failMessages)]
            console.warn('选品失败详情：', uniqueErrors)
          }

          // 清空选择列表，移除已选品的商品
          this.selectedProducts = this.selectedProducts.filter(
            (product) => !this.isProductAlreadyChosen(product)
          )
          this.updateSelectAllState()
        })
        .catch(() => {
          // 取消操作，不做任何处理
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.product-list {
  width: 100%;
  min-width: 0;
}

.selection-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 20px;

  .selection-controls {
    display: flex;
    align-items: center;
    gap: 15px;

    .selected-count {
      font-size: 14px;
      color: #606266;
    }
  }
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.product-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  background: #fff;
  position: relative;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  &.selected {
    border-color: #409eff;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
  }

  &.already-chosen {
    background: #f5f7fa;
    border-color: #67c23a;
    opacity: 0.8;

    &:hover {
      transform: none;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .product-info {
      opacity: 0.7;
    }
  }
}

.product-checkbox {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 10;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  padding: 4px;
  backdrop-filter: blur(2px);
}

.chosen-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 4px;
  padding: 2px;
  backdrop-filter: blur(2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.product-image {
  height: 200px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.product-info {
  padding: 15px;
}

.product-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0 0 10px 0;
}

.price-info {
  margin-bottom: 10px;

  .current-price {
    font-size: 18px;
    font-weight: 600;
    color: #e6a23c;
  }
}

.commission-info {
  margin-bottom: 8px;
}

.product-id-info {
  margin-bottom: 8px;

  .product-id {
    font-size: 12px;
    color: #606266;
    font-weight: 500;
  }
}

.shop-info {
  .shop-name {
    font-size: 12px;
    color: #909399;
  }
}

.empty-state {
  text-align: center;
  padding: 60px 0;

  .custom-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
      opacity: 0.6;
    }

    .empty-text {
      font-size: 14px;
      color: #909399;
    }
  }
}

.main-content {
  margin-top: 20px;
}

.category-tabs {
  margin-bottom: 20px;

  ::v-deep .el-tabs__header {
    margin: 0 0 20px 0;
  }

  ::v-deep .el-tabs__nav-wrap {
    padding: 0 20px;
  }

  ::v-deep .el-tabs__item {
    font-size: 14px;
    font-weight: 500;
    padding: 0 20px;
    height: 40px;
    line-height: 40px;
  }

  ::v-deep .el-tabs__item.is-active {
    color: #409eff;
    font-weight: 600;
  }

  ::v-deep .el-tabs__active-bar {
    background-color: #409eff;
  }
}

.pagination-wrapper {
  text-align: center;
  margin-top: 20px;

  .no-more-data {
    color: #909399;
    font-size: 14px;
    padding: 20px 0;
  }
}
</style>
