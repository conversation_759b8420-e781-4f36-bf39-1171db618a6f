import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/platformProduct/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/platformProduct/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function addPlatformProduct(data) {
  return request({
    url: '/platformProduct/add',
    method: 'post',
    data: data,
  })
}

export function editPlatformProduct(data) {
  return request({
    url: '/platformProduct/edit',
    method: 'post',
    data: data,
  })
}

export function delPlatformProduct(id) {
  return request({
    url: '/platformProduct/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

//----------PlatformProduct结束----------
