import { optionsApi } from '@/api'
import { userInfoStatus } from '@/utils/constant'

export function getUserInfoStatus() {
  return userInfoStatus
}

export async function getPlatformCategoryOptions(params) {
  let res = await optionsApi.getPlatformCategoryOptions(params)
  return getOptions(res)
}

function getOptions(res) {
  if (res.code == 0) {
    return res.data
  }
  return []
}

// 操作类型
export const actionTypeOptions = [
  { label: 'JSON', value: 'json' },
  { label: 'BOOL', value: 'bool' },
  { label: 'STRING', value: 'string' },
  { label: 'INT', value: 'int' },
]

// 是否可用
export const enableOptions = [
  { label: '是', value: true },
  { label: '否', value: false },
]

// 状态
export const statusOptions = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 2 },
  { label: '屏蔽', value: 3 },
]

// ip类型
export const ipTypeOptions = [
  { label: '全部', value: -1 },
  { label: '访问', value: 1 },
  { label: '点击', value: 2 },
]

// 平台类型
export const platformOptions = [
  { label: 'Unknown', value: -1 },
  { label: 'PC', value: 0 },
  { label: 'IOS', value: 1 },
  { label: 'Android', value: 2 },
]

export const statsStatuOptions = [
  { label: '全部', value: -1 },
  { label: '正常', value: 0 },
  { label: '异常', value: 1 },
]

// 时区
export const utcTimeZoneOptions = [
  { label: 'UTC (GMT) - 格林威治（英国），冰岛，西非部分地区', value: 0 },
  { label: 'UTC+1 - 西欧（西班牙、法国、德国）、比利时、阿尔及利亚', value: 1 },
  { label: 'UTC+2 - 南非、希腊、以色列、埃及', value: 2 },
  { label: 'UTC+3 - 俄罗斯莫斯科、沙特阿拉伯、坦桑尼亚', value: 3 },
  { label: 'UTC+4 - 阿联酋、阿塞拜疆、亚美尼亚', value: 4 },
  { label: 'UTC+5 - 哈萨克斯坦、乌兹别克斯坦', value: 5 },
  { label: 'UTC+6 - 孟加拉国、吉尔吉斯斯坦', value: 6 },
  { label: 'UTC+7 - 泰国、越南、柬埔寨', value: 7 },
  { label: 'UTC+8 - 中国、新加坡、马来西亚、澳大利亚西部', value: 8 },
  { label: 'UTC+9 - 日本、韩国', value: 9 },
  { label: 'UTC+10 - 澳大利亚东部（悉尼、墨尔本）', value: 10 },
  { label: 'UTC-1 - 亚速尔群岛（葡萄牙），佛得角岛', value: -1 },
  { label: 'UTC-2 - 南乔治亚岛（南大西洋）', value: -2 },
  { label: 'UTC-3 - 阿根廷、巴西、乌拉圭', value: -3 },
  { label: 'UTC-4 - 加拿大、玻利维亚、委内瑞拉', value: -4 },
  { label: 'UTC-5 - 美国东部（纽约、华盛顿）', value: -5 },
  { label: 'UTC-6 - 美国中部（芝加哥、墨西哥城）', value: -6 },
  { label: 'UTC-7 - 美国山地时间（丹佛）', value: -7 },
  { label: 'UTC-8 - 美国西部（洛杉矶、旧金山）', value: -8 },
  { label: 'UTC-9 - 阿拉斯加', value: -9 },
  { label: 'UTC-10 - 夏威夷', value: -10 },
  { label: 'UTC-11 - 美属萨摩亚岛', value: -11 },
  { label: 'UTC-12 - 贝克岛、豪兰岛（无人岛）', value: -12 },
]

// 通用时间段快捷选项
const day = 60 * 1000 * 60 * 24 // 一天的毫秒数
export const commonTimePickerOptions = {
  shortcuts: [
    {
      text: '昨日',
      onClick(picker) {
        let start = new Date()
        let end = new Date()
        start.setTime(start.getTime() - day)
        end = start
        picker.$emit('pick', [start, end])
      },
    },
    {
      text: '今日',
      onClick(picker) {
        const start = new Date()
        const end = new Date()
        picker.$emit('pick', [start, end])
      },
    },
    {
      text: '最近三日',
      onClick(picker) {
        const start = new Date()
        const end = new Date()
        start.setTime(start.getTime() - day * 2)
        picker.$emit('pick', [start, end])
      },
    },
    {
      text: '最近一周',
      onClick(picker) {
        const start = new Date()
        const end = new Date()
        start.setTime(start.getTime() - day * 6)
        picker.$emit('pick', [start, end])
      },
    },
    {
      text: '最近一个月',
      onClick(picker) {
        const start = new Date()
        const end = new Date()
        start.setTime(start.getTime() - day * 29)
        picker.$emit('pick', [start, end])
      },
    },
  ],
}

export const getTodayStartTimeStr = () => {
  var currentDate = new Date()
  var year = currentDate.getFullYear().toString()
  var month = (currentDate.getMonth() + 1).toString()
  if (month.length === 1) {
    month = '0' + month
  }
  var date = currentDate.getDate().toString()
  if (date.length === 1) {
    date = '0' + date
  }
  var hour = '00'
  var minute = '00'
  var second = '00'
  return year + '-' + month + '-' + date + ' ' + hour + ':' + minute + ':' + second
}

export const getCurrentTimeStr = (addHours) => {
  var currentDate = new Date()
  var year = currentDate.getFullYear().toString()
  var month = (currentDate.getMonth() + 1).toString()
  if (month.length === 1) {
    month = '0' + month
  }
  var date = currentDate.getDate().toString()
  if (date.length === 1) {
    date = '0' + date
  }
  var hour = currentDate.getHours().toString()
  if (addHours && hour > 0) hour = parseInt(hour) + addHours
  if (hour.length === 1) {
    hour = '0' + hour
  }
  var minute = currentDate.getMinutes().toString()
  if (minute.length === 1) {
    minute = '0' + minute
  }
  var second = currentDate.getSeconds().toString()
  if (second.length === 1) {
    second = '0' + second
  }

  return year + '-' + month + '-' + date + ' ' + hour + ':' + minute + ':' + second
}
