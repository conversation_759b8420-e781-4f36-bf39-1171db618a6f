<script>
export default {
  name: 'MenuItem',
  functional: true,
  props: {
    icon: {
      type: String,
      default: '',
    },
    title: {
      type: String,
      default: '',
    },
  },
  render(h, context) {
    const { icon, title } = context.props
    const vnodes = []

    if (icon) {
      if (icon.startsWith('el-icon')) {
        vnodes.push(<i class={icon + ' e-icon'} />)
      } else {
        vnodes.push(<svg-icon icon-class={icon} />)
      }
    }

    if (title) {
      vnodes.push(<span slot="title">{title}</span>)
    }
    return vnodes
  },
}
</script>

<style lang="scss" scoped>
.openSidebar .e-icon {
  margin-right: 16px;
  width: 14px;
}
.hideSidebar .e-icon {
  margin-left: 14px;
}
</style>
