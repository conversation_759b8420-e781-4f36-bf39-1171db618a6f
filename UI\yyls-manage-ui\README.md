## 项目说明

项目基于`Vue2.x + ElementUI`开发

Vue文档：https://cn.vuejs.org/

ElementUI文档：https://element.eleme.cn/#/zh-CN/component/quickstart

使用模板[vue-admin-template](https://github.com/PanJiaChen/vue-admin-template)快速搭建，文档地址：https://panjiachen.gitee.io/vue-element-admin-site/zh/

## 环境搭建

> 提供两种运行方式，1：手动搭建Vue开发环境；2：使用Docker已配置好的环境

### 方式一：手动搭建Vue开发环境

1. 下载安装node.js 

   下载地址：http://nodejs.cn/download/

2. npm配置淘宝源

   ```bash
   npm config set registry https://registry.npm.taobao.org
   npm config set sass_binary_site=https://npm.taobao.org/mirrors/node-sass/
   ```

   

3. 安装yarn（可选）

   Yarn 是为了弥补 npm 的一些缺陷而出现的，下载地址：https://yarn.bootcss.com/

   **yarn配置淘宝源**：

   ```bash
   yarn config set registry https://registry.npm.taobao.org -g
   yarn config set sass_binary_site http://cdn.npm.taobao.org/dist/node-sass -g
   ```

4. 安装Vue CLI

   如果需要创建Vue项目的话，则需要安装

   文档地址：https://cli.vuejs.org/zh/guide/

   ```bash
   npm install -g @vue/cli
   # OR
   yarn global add @vue/cli
   ```



### 方式二：Docker

项目根目录提供了`Dockerfile`构建Vue开发环境的镜像，以及配套的`docker-compose`文件（如有不足之处，请自行调整）

运行`dokcer-compose up`命令构建、启动容器（如果`vscode`安装了Docker插件，右键单击docker-compose文件执行Compose Up）

**注意：** `vue.config.js`文件中需要将以下注释打开，否则文件修改后无法触发热重载

```js
configureWebpack: {
	...
    devServer: {
      watchOptions: {
        ignored: /node_modules/, // 设置不监听的目录
        aggregateTimeout: 500, // 文件变动后多久发起构建
        poll: 500 // 通过向系统轮询文件是否变化来判断文件是否改变，poll为每秒询问次数
      },
    }
  },
```

进入容器中即可开始进行开发



## 运行

### 安装依赖包

在根目录下执行以下命令安装相关的依赖包

```bash
npm install
# OR
yarn install
```

### 运行开发版本

执行命令以下命令开始进行开发（具体命令内容请查看`package.json`文件里的`scripts`属性）

```bash
npm run dev
# OR
yarn dev
```

开发环境默认使用的api接口地址是：`http://localhost:9330`，如需修改，请在`vue.config.js`文件中的`devServer`属性里进行修改，如下所示：

```js
devServer: {
    ...
    proxy: {
      [process.env.VUE_APP_BASE_API]: {
        target: `http://localhost:9330`, // 测试时的api服务器，在这里改
        // target: `http://host.docker.internal:9330`, // 如果使用容器，则通过http://host.docker.internal访问宿主机
        changeOrigin: true,
      }
    },
  },
```

打开浏览器，输入地址：http://localhost:9300，进行预览。默认端口是`9300`，如需修改，可在`vue.config.js`中修改

### 构建部署版本

```bash
npm run build
# OR
yarn build
```

打包完成后会在项目根目录生成`dist`文件夹，将文件夹里面的内容丢在已经部署的`Api项目`根目录即可

**注意：**后端项目需配置URL重写，将**非api类型的请求**都交由前端进行处理，参考：

```xml
<system.webServer>
    <rewrite> 
       <rules> 
         <rule name="Index Rule" stopProcessing="true"> 
           <match url="^index\.html$" ignoreCase="false" /> 
           <action type="None" /> 
         </rule> 
         <rule name="API Rule" stopProcessing="true"> 
           <match url="^(api)(.*)$" /> 
           <action type="None" /> 
         </rule> 
         <rule name="Vue Rule" stopProcessing="true"> 
           <match url="(.*)" /> 
           <conditions logicalGrouping="MatchAll"> 
             <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" /> 
             <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" /> 
           </conditions> 
           <action type="Rewrite" url="/" /> 
         </rule> 
       </rules> 
     </rewrite> 
  </system.webServer>
```

