// import Cookies from 'js-cookie'
import store from '@/store'
import Vue from 'vue'

const TokenKey = 'admin_token'
const hour = 60 * 60 * 1000
const day = 24 * hour

export function getToken() {
  return Vue.ls.get(TokenKey)
  // return Cookies.get(TokenKey)
}

export function setToken(token) {
  Vue.ls.set(TokenKey, token, 7 * 24 * hour)
  // Cookies.set(TokenKey, token)
}

export function removeToken() {
  // return Cookies.remove(TokenKey)
  return Vue.ls.remove(TokenKey)
}

export function hasPermission(permissionStr) {
  if (permissionStr && permissionStr.length > 0) {
    const permissions = store.getters.permissions
    if (permissions.includes('*:*:*')) {
      return true
    }
    const hasPermission = permissions.includes(permissionStr)
    return hasPermission
  } else {
    return false
  }
}

const userLoginParamsExpires = 30 * day
const userIdKey = 'userId'
const userNameKey = 'username'
const passwordKey = 'password'
const rememberMeKey = 'rememberMe'

export function setUserName(userName) {
  Vue.ls.set(userName<PERSON>ey, userName, userLoginParamsExpires)
}

export function setUserId(userId) {
  Vue.ls.set(userIdKey, userId, userLoginParamsExpires)
}

export function setPassword(password) {
  Vue.ls.set(passwordKey, password, userLoginParamsExpires)
}

export function setRememberMe(rememberMe) {
  Vue.ls.set(rememberMeKey, rememberMe, userLoginParamsExpires)
}

export function getUesrName() {
  return Vue.ls.get(userNameKey)
}

export function getPassword() {
  return Vue.ls.get(passwordKey)
}

export function getRememberMe() {
  return Vue.ls.get(rememberMeKey)
}

export function removeUserLoginParams() {
  Vue.ls.remove(userNameKey)
  Vue.ls.remove(passwordKey)
  Vue.ls.remove(rememberMeKey)
}
