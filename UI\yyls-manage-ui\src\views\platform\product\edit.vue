<template>
  <div class="app-container">
    <el-card>
      <div slot="header" class="page-header">
        <span class="page-title">{{ title }}</span>
        <div>
          <el-button @click="close">取消</el-button>
          <el-button type="primary" @click="submitForm">保存</el-button>
        </div>
      </div>

      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <!-- 基本信息 -->
        <el-card class="section-card info-card">
          <div slot="header">
            <i class="el-icon-info" style="color: #409eff; margin-right: 8px"></i>
            <span style="font-weight: bold; color: #303133">基本信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="商品ID" prop="productId">
                <div class="readonly-label">
                  <i class="el-icon-goods readonly-icon"></i>
                  <span>{{ form.productId || '-' }}</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="平台ID" prop="platformId">
                <div class="readonly-label">
                  <i class="el-icon-platform readonly-icon"></i>
                  <span>{{ form.platformId || '-' }}</span>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="商品标题" prop="title">
            <el-input
              v-model="form.title"
              placeholder="请输入商品标题"
              maxlength="200"
              show-word-limit
              clearable
            >
              <i slot="prefix" class="el-icon-document"></i>
            </el-input>
          </el-form-item>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="销售价格" prop="salePrice">
                <div class="readonly-label readonly-price">
                  <span class="price-symbol">¥</span>
                  <span class="price-value">{{ formatPrice(form.salePrice) || '0.00' }}</span>
                </div>
                <div class="form-tip">单位：元</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="佣金比例" prop="commissionServiceRatio">
                <div class="readonly-label">
                  <i class="el-icon-percent readonly-icon"></i>
                  <span>{{ formatCommissionRatio(form.commissionServiceRatio) || '0%' }}</span>
                </div>
                <div class="form-tip">佣金比例</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="佣金金额">
                <div class="readonly-label readonly-price">
                  <span class="price-symbol">¥</span>
                  <span class="price-value commission-amount">{{
                    commissionAmount || '0.00'
                  }}</span>
                </div>
                <div class="form-tip">单位：元</div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20" class="switch-row">
            <el-col :span="12">
              <el-form-item label="是否启用" prop="enable">
                <el-switch
                  v-model="form.enable"
                  active-color="#67C23A"
                  inactive-color="#909399"
                  active-text="启用"
                  inactive-text="禁用"
                >
                </el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="首页推荐" prop="isRecommend">
                <el-switch
                  v-model="form.isRecommend"
                  active-color="#409EFF"
                  inactive-color="#909399"
                  active-text="推荐"
                  inactive-text="不推荐"
                >
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 商品图片 -->
        <el-card class="section-card">
          <div slot="header">
            <i class="el-icon-picture" style="color: #67c23a; margin-right: 8px"></i>
            <span style="font-weight: bold; color: #303133">商品图片</span>
          </div>
          <el-form-item label="商品图片" prop="imageUrls">
            <!-- 上传图片区域 -->
            <div class="upload-section">
              <x-upload
                :upload-type="uploadImageType"
                :upload-temp-id="uploadTempId"
                :upload-temp="uploadTemp"
                v-model="uploadedImages"
                :multiple="true"
                :limit="10"
                :show-file-list="false"
                :bind-url="true"
                @success="handleUploadSuccess"
                @remove="handleUploadRemove"
              ></x-upload>
              <div class="upload-tip">支持 jpg、png、gif、webp 格式，最多上传10张图片</div>
            </div>

            <!-- 添加图片链接区域 -->
            <div class="add-url-section">
              <el-input
                v-model="newImageUrl"
                placeholder="输入图片链接地址"
                style="width: 300px; margin-right: 10px"
              >
                <i slot="prefix" class="el-icon-link"></i>
              </el-input>
              <el-button type="primary" @click="addImageUrl" icon="el-icon-plus"
                >添加链接</el-button
              >
            </div>

            <!-- 图片预览列表 -->
            <div v-if="uploadedImages.length > 0" class="image-preview-list">
              <div v-for="(url, index) in uploadedImages" :key="index" class="image-item">
                <div class="image-index">{{ index + 1 }}</div>
                <img
                  :src="url"
                  :alt="`商品图片${index + 1}`"
                  class="preview-image"
                  @error="handleImageError(index)"
                />
                <div class="image-actions">
                  <el-button
                    type="text"
                    size="mini"
                    @click="moveImageLeft(index)"
                    icon="el-icon-arrow-left"
                    :disabled="index === 0"
                    class="move-btn move-left"
                  ></el-button>
                  <el-button
                    type="text"
                    size="mini"
                    @click="removeImage(index)"
                    icon="el-icon-delete"
                    class="delete-btn"
                    >删除</el-button
                  >
                  <el-button
                    type="text"
                    size="mini"
                    @click="moveImageRight(index)"
                    icon="el-icon-arrow-right"
                    :disabled="index === uploadedImages.length - 1"
                    class="move-btn move-right"
                  ></el-button>
                </div>
              </div>
            </div>

            <!-- 隐藏的图片链接存储 -->
            <el-input
              v-model="form.imageUrls"
              type="textarea"
              :rows="2"
              style="display: none"
            ></el-input>
          </el-form-item>
        </el-card>

        <!-- 描述信息 -->
        <el-card class="section-card">
          <div slot="header">
            <i class="el-icon-document" style="color: #e6a23c; margin-right: 8px"></i>
            <span style="font-weight: bold; color: #303133">描述信息</span>
          </div>
          <el-form-item label="描述图片" prop="descImageUrls">
            <!-- 上传描述图片区域 -->
            <div class="upload-section">
              <x-upload
                :upload-type="uploadImageType"
                :upload-temp-id="uploadTempId"
                :upload-temp="uploadTemp"
                v-model="uploadedDescImages"
                :multiple="true"
                :limit="20"
                :show-file-list="false"
                :bind-url="true"
                @success="handleDescUploadSuccess"
                @remove="handleDescUploadRemove"
              ></x-upload>
              <div class="upload-tip">支持 jpg、png、gif、webp 格式，最多上传20张描述图片</div>
            </div>

            <!-- 添加描述图片链接区域 -->
            <div class="add-url-section">
              <el-input
                v-model="newDescImageUrl"
                placeholder="输入描述图片链接地址"
                style="width: 300px; margin-right: 10px"
              >
                <i slot="prefix" class="el-icon-link"></i>
              </el-input>
              <el-button type="primary" @click="addDescImageUrl" icon="el-icon-plus"
                >添加链接</el-button
              >
            </div>

            <!-- 描述图片预览列表 -->
            <div v-if="uploadedDescImages.length > 0" class="image-preview-list">
              <div v-for="(url, index) in uploadedDescImages" :key="index" class="image-item">
                <div class="image-index">{{ index + 1 }}</div>
                <img
                  :src="url"
                  :alt="`描述图片${index + 1}`"
                  class="preview-image"
                  @error="handleDescImageError(index)"
                />
                <div class="image-actions">
                  <el-button
                    type="text"
                    size="mini"
                    @click="moveDescImageLeft(index)"
                    icon="el-icon-arrow-left"
                    :disabled="index === 0"
                    class="move-btn move-left"
                  ></el-button>
                  <el-button
                    type="text"
                    size="mini"
                    @click="removeDescImage(index)"
                    icon="el-icon-delete"
                    class="delete-btn"
                    >删除</el-button
                  >
                  <el-button
                    type="text"
                    size="mini"
                    @click="moveDescImageRight(index)"
                    icon="el-icon-arrow-right"
                    :disabled="index === uploadedDescImages.length - 1"
                    class="move-btn move-right"
                  ></el-button>
                </div>
              </div>
            </div>

            <!-- 隐藏的描述图片链接存储 -->
            <el-input
              v-model="form.descImageUrls"
              type="textarea"
              :rows="2"
              style="display: none"
            ></el-input>
          </el-form-item>
        </el-card>

        <!-- 严选过程 -->
        <el-card class="section-card selection-card">
          <div slot="header">
            <i class="el-icon-star-on" style="color: #f56c6c; margin-right: 8px"></i>
            <span style="font-weight: bold; color: #303133">严选过程</span>
            <div style="float: right">
              <el-button
                type="primary"
                size="mini"
                @click="openContentDialog('selection')"
                icon="el-icon-plus"
                >添加内容</el-button
              >
            </div>
          </div>
          <el-form-item label="严选过程">
            <!-- 严选过程内容列表 -->
            <div v-if="selectionProcessList.length > 0" class="content-list selection-content-list">
              <div
                v-for="(item, index) in selectionProcessList"
                :key="index"
                class="content-item selection-content-item"
              >
                <div class="content-index">{{ index + 1 }}</div>
                <div class="content-body">
                  <div v-if="item.title" class="content-title">{{ item.title }}</div>
                  <div class="content-text" v-html="item.text"></div>
                  <div v-if="item.imageUrl" class="content-image">
                    <div
                      v-if="Array.isArray(item.imageUrl) && item.imageUrl.length > 1"
                      class="multi-image-container"
                    >
                      <img
                        v-for="(imgUrl, imgIndex) in item.imageUrl"
                        :key="imgIndex"
                        :src="imgUrl"
                        :alt="`严选过程图片${index + 1}-${imgIndex + 1}`"
                        class="content-preview-image multi-image"
                      />
                    </div>
                    <img
                      v-else
                      :src="Array.isArray(item.imageUrl) ? item.imageUrl[0] : item.imageUrl"
                      :alt="`严选过程图片${index + 1}`"
                      class="content-preview-image"
                    />
                  </div>
                  <div v-if="item.videoUrl" class="content-video">
                    <video
                      :src="item.videoUrl"
                      :poster="item.videoCoverUrl"
                      controls
                      class="video-player"
                    >
                      您的浏览器不支持视频播放
                    </video>
                  </div>
                </div>
                <div class="content-actions">
                  <el-button
                    type="text"
                    size="mini"
                    @click="editContentItem(index, 'selection')"
                    icon="el-icon-edit"
                    >编辑</el-button
                  >
                  <el-button
                    type="text"
                    size="mini"
                    @click="removeContentItem(index, 'selection')"
                    icon="el-icon-delete"
                    class="delete-action"
                    >删除</el-button
                  >
                </div>
              </div>
            </div>
            <div v-else class="empty-content">
              <i class="el-icon-star-on"></i>
              <p>暂无严选过程内容，点击上方"添加内容"按钮开始添加</p>
            </div>
            <div class="form-tip">描述商品的严选过程，包括选品理由、质量检测等</div>
          </el-form-item>
        </el-card>

        <!-- 社群素材 -->
        <el-card class="section-card community-card">
          <div slot="header">
            <i class="el-icon-share" style="color: #909399; margin-right: 8px"></i>
            <span style="font-weight: bold; color: #303133">社群素材</span>
            <div style="float: right">
              <el-button
                type="primary"
                size="mini"
                @click="openContentDialog('community')"
                icon="el-icon-plus"
                >添加内容</el-button
              >
            </div>
          </div>
          <el-form-item label="社群素材">
            <!-- 社群素材内容列表 -->
            <div
              v-if="communityMaterialList.length > 0"
              class="content-list community-content-list"
            >
              <div
                v-for="(item, index) in communityMaterialList"
                :key="index"
                class="content-item community-content-item"
              >
                <div class="content-index">{{ index + 1 }}</div>
                <div class="content-body">
                  <div v-if="item.title" class="content-title">{{ item.title }}</div>
                  <div class="content-text" v-html="item.text"></div>
                  <div v-if="item.imageUrl" class="content-image">
                    <div
                      v-if="Array.isArray(item.imageUrl) && item.imageUrl.length > 1"
                      class="multi-image-container"
                    >
                      <img
                        v-for="(imgUrl, imgIndex) in item.imageUrl"
                        :key="imgIndex"
                        :src="imgUrl"
                        :alt="`社群素材图片${index + 1}-${imgIndex + 1}`"
                        class="content-preview-image multi-image"
                      />
                    </div>
                    <img
                      v-else
                      :src="Array.isArray(item.imageUrl) ? item.imageUrl[0] : item.imageUrl"
                      :alt="`社群素材图片${index + 1}`"
                      class="content-preview-image"
                    />
                  </div>
                  <div v-if="item.videoUrl" class="content-video">
                    <video
                      :src="item.videoUrl"
                      :poster="item.videoCoverUrl"
                      controls
                      class="video-player"
                    >
                      您的浏览器不支持视频播放
                    </video>
                  </div>
                </div>
                <div class="content-actions">
                  <el-button
                    type="text"
                    size="mini"
                    @click="editContentItem(index, 'community')"
                    icon="el-icon-edit"
                    >编辑</el-button
                  >
                  <el-button
                    type="text"
                    size="mini"
                    @click="removeContentItem(index, 'community')"
                    icon="el-icon-delete"
                    class="delete-action"
                    >删除</el-button
                  >
                </div>
              </div>
            </div>
            <div v-else class="empty-content">
              <i class="el-icon-share"></i>
              <p>暂无社群素材内容，点击上方"添加内容"按钮开始添加</p>
            </div>
            <div class="form-tip">用于社群分享的素材内容，包括推广文案、图片等</div>
          </el-form-item>
        </el-card>
      </el-form>
    </el-card>
    <!-- 添加内容弹出层 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="contentDialogVisible"
      width="800px"
      @close="resetDialog"
      class="content-dialog"
    >
      <el-form :model="dialogForm" label-width="80px" class="dialog-form">
        <el-form-item v-if="dialogType === 'selection'" label="标题" class="title-content-item">
          <el-input
            v-model="dialogForm.title"
            placeholder="请输入内容标题"
            maxlength="100"
            show-word-limit
            clearable
          >
            <i slot="prefix" class="el-icon-edit"></i>
          </el-input>
        </el-form-item>
        <el-form-item v-if="dialogType === 'community'" label="内容类型" class="content-type-item">
          <el-radio-group v-model="dialogForm.contentType" @change="onContentTypeChange">
            <el-radio-button label="text">文字内容</el-radio-button>
            <el-radio-button label="image">图片内容</el-radio-button>
            <el-radio-button label="video">视频内容</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="
            dialogType === 'selection' ||
            (dialogType === 'community' && dialogForm.contentType === 'image')
          "
          label="配图"
          class="image-content-item"
        >
          <div class="image-upload-wrapper">
            <x-upload
              :upload-type="uploadImageType"
              :upload-temp-id="uploadTempId"
              :upload-temp="uploadTemp"
              v-model="dialogForm.imageUrl"
              :src="dialogForm.imageUrl"
              :can-choose="true"
              :multiple="true"
              :limit="5"
              :bind-url="true"
            ></x-upload>
            <div class="upload-tip">支持上传图片，建议尺寸：800x600px</div>
          </div>
        </el-form-item>
        <el-form-item
          v-if="
            dialogType === 'selection' ||
            (dialogType === 'community' && dialogForm.contentType === 'video')
          "
          label="视频"
          class="video-content-item"
        >
          <div class="video-upload-wrapper">
            <x-video-upload
              :video-upload-type="uploadVideoType"
              :cover-upload-type="uploadImageType"
              :upload-temp-id="uploadTempId"
              :upload-temp="uploadTemp"
              v-model="dialogForm.videoData"
              :src="dialogForm.videoData"
              :can-choose="false"
              :show-cover="false"
              :bind-url="true"
              :max-size="50"
              @error="handleVideoUploadError"
            ></x-video-upload>
          </div>
        </el-form-item>
        <el-form-item
          v-if="
            dialogType === 'selection' ||
            (dialogType === 'community' && dialogForm.contentType === 'text')
          "
          label="文本内容"
          class="text-content-item"
        >
          <tinymce
            v-model="dialogForm.text"
            :height="300"
            :plugins="['emoticons']"
            :toolbar="'bold italic underline | forecolor backcolor | emoticons | undo redo'"
          />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="contentDialogVisible = false" size="medium">取消</el-button>
        <el-button type="primary" @click="saveDialogContent" size="medium">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { enableOptions } from '@/utils/options'
import { UPLOAD_TYPE } from '@/utils/constant'
import { platformProductApi } from '@/api'
import XUpload from '@/components/XUpload'
import Tinymce from '@/components/Tinymce'

export default {
  components: {
    XUpload,
    Tinymce,
  },
  data() {
    return {
      title: '新增选品信息',
      form: {
        id: null,
        productId: '',
        platformId: '',
        title: '',
        imageUrls: '',
        descImageUrls: '',
        salePrice: 0,
        commissionServiceRatio: 0,
        enable: true,
        isRecommend: false,
        customData: {},
      },
      newImageUrl: '', // 新图片链接输入框
      uploadedImages: [], // 上传的图片列表
      newDescImageUrl: '', // 新描述图片链接输入框
      uploadedDescImages: [], // 上传的描述图片列表

      // 严选过程和社群素材列表
      selectionProcessList: [],
      communityMaterialList: [],

      // 弹出层相关
      contentDialogVisible: false,
      dialogTitle: '',
      dialogType: '', // 'selection' 或 'community'
      editingIndex: -1, // 编辑时的索引，-1表示新增
      dialogForm: {
        title: '',
        text: '',
        imageUrl: '',
        videoData: {
          videoUrl: '',
          coverUrl: '',
        },
        contentType: 'text', // text, image, video
      },

      rules: {
        productId: [{ required: true, message: '请输入商品ID', trigger: 'blur' }],
        title: [{ required: true, message: '请输入商品标题', trigger: 'blur' }],
        salePrice: [{ required: true, message: '请输入销售价格', trigger: 'blur' }],
        commissionServiceRatio: [{ required: true, message: '请输入佣金比例', trigger: 'blur' }],
      },
      uploadImageType: UPLOAD_TYPE.platformProductImage,
      uploadVideoType: UPLOAD_TYPE.platformProductVideo,
      enableOptions,
    }
  },
  created() {
    this.form.id = this.$route.query.id
    if (!this.form.id) {
      this.$xMsgError('参数错误！')
      this.$router.back()
    }
    this.edit()
  },
  computed: {
    // 计算佣金金额
    commissionAmount() {
      if (this.form.salePrice && this.form.commissionServiceRatio) {
        return ((this.form.salePrice * this.form.commissionServiceRatio) / (1000000 * 100)).toFixed(
          2
        )
      }
      return '0.00'
    },
    // 图片URL数组
    imageUrlArray() {
      if (this.form.imageUrls) {
        // 清理可能存在的反引号和多余空格
        const cleanUrls = this.form.imageUrls
          .replace(/`/g, '') // 移除反引号
          .trim() // 移除前后空格
        return cleanUrls
          .split(',')
          .map((url) => url.trim())
          .filter((url) => url)
      }
      return []
    },
    // 描述图片URL数组
    descImagesArray() {
      if (this.form.descImageUrls) {
        // 清理可能存在的反引号和多余空格
        const cleanUrls = this.form.descImageUrls
          .replace(/`/g, '') // 移除反引号
          .trim() // 移除前后空格
        return cleanUrls
          .split(',')
          .map((url) => url.trim())
          .filter((url) => url)
      }
      return []
    },
    uploadTemp() {
      return this.form.platformId
    },
    uploadTempId() {
      return this.form.productId
    },
  },
  methods: {
    async edit() {
      this.title = '修改选品信息'
      this.$xloading.show()
      const res = await platformProductApi.getDetail(this.form.id)
      if (res.code == 0) {
        // 解析 customData 中的 selectionProcessList 和 communityMaterialList
        if (res.data.customData) {
          try {
            const customData = JSON.parse(res.data.customData)
            if (customData.selectionProcessList) {
              this.selectionProcessList = customData.selectionProcessList
            }
            if (customData.communityMaterialList) {
              this.communityMaterialList = customData.communityMaterialList
            }
          } catch (error) {
            console.warn('解析customData失败:', error)
          }
        }

        this.form = {
          ...res.data,
        }
        // 初始化已上传图片数组
        this.uploadedImages = this.imageUrlArray.slice()
        this.uploadedDescImages = this.descImagesArray.slice()
      } else {
        this.$xMsgError('加载失败！' + res.msg)
      }
      this.$xloading.hide()
    },

    reset() {
      this.form = {
        name: undefined,
        content: '',
        enable: true,
      }
      this.$xResetForm('form')
    },

    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        if (valid) {
          this.$xloading.show()
          const data = Object.assign({}, this.form)
          data.customData = JSON.stringify({
            selectionProcessList: this.selectionProcessList,
            communityMaterialList: this.communityMaterialList,
          })
          const { id } = data
          const res = id
            ? await platformProductApi.editPlatformProduct(data)
            : await platformProductApi.addPlatformProduct(data)
          this.$xloading.hide()

          if (res.code == 0) {
            this.$xMsgSuccess(id ? '修改成功' : '添加成功')
            // 提交成功后的处理：跳转或清空表单
            this.close()
          } else {
            this.$xMsgError('操作失败！' + res.msg)
          }
        }
      })
    },

    // 通用图片操作方法
    addImageUrl(type = 'main') {
      const urlField = type === 'main' ? 'newImageUrl' : 'newDescImageUrl'
      const url = this[urlField].trim()

      if (!url) {
        this.$message.warning('请输入图片链接')
        return
      }

      // 验证URL格式
      const urlPattern = /^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)(\?.*)?$/i
      if (!urlPattern.test(url)) {
        this.$message.warning('请输入有效的图片链接（支持jpg、png、gif、webp格式）')
        return
      }

      const imageArray = type === 'main' ? this.uploadedImages : this.uploadedDescImages
      imageArray.push(url)
      this.syncImages(type)
      this[urlField] = ''
      this.$message.success('图片添加成功')
    },

    // 通用删除图片方法
    removeImage(index, type = 'main') {
      const imageArray = type === 'main' ? this.uploadedImages : this.uploadedDescImages
      imageArray.splice(index, 1)
      this.syncImages(type)
      this.$message.success('图片删除成功')
    },

    // 通用左移图片方法
    moveImageLeft(index, type = 'main') {
      if (index > 0) {
        const imageArray = type === 'main' ? this.uploadedImages : this.uploadedDescImages
        const temp = imageArray[index]
        this.$set(imageArray, index, imageArray[index - 1])
        this.$set(imageArray, index - 1, temp)
        this.syncImages(type)
        this.$message.success('图片位置调整成功')
      }
    },

    // 通用右移图片方法
    moveImageRight(index, type = 'main') {
      const imageArray = type === 'main' ? this.uploadedImages : this.uploadedDescImages
      if (index < imageArray.length - 1) {
        const temp = imageArray[index]
        this.$set(imageArray, index, imageArray[index + 1])
        this.$set(imageArray, index + 1, temp)
        this.syncImages(type)
        this.$message.success('图片位置调整成功')
      }
    },

    // 通用图片加载错误处理
    handleImageError(index, type = 'main') {
      const imageType = type === 'main' ? '图片' : '描述图片'
      this.$message.error(`第${index + 1}张${imageType}加载失败，请检查链接是否有效`)
    },

    // 通用同步图片方法
    syncImages(type = 'main') {
      if (type === 'main') {
        this.form.imageUrls = this.uploadedImages.join(',')
      } else if (type === 'desc') {
        this.form.descImageUrls = this.uploadedDescImages.join(',')
      }
    },

    // 通用内容弹出层操作
    openContentDialog(type) {
      const titles = {
        selection: '添加严选过程内容',
        community: '添加社群素材内容',
      }
      this.dialogTitle = titles[type]
      this.dialogType = type
      this.editingIndex = -1
      this.contentDialogVisible = true
    },

    // 通用编辑内容项目
    editContentItem(index, type) {
      const titles = {
        selection: '编辑严选过程内容',
        community: '编辑社群素材内容',
      }
      const lists = {
        selection: this.selectionProcessList,
        community: this.communityMaterialList,
      }

      this.dialogTitle = titles[type]
      this.dialogType = type
      this.editingIndex = index
      const item = lists[type][index]
      this.dialogForm.title = type === 'selection' ? item.title || '' : ''
      this.dialogForm.text = item.text
      this.dialogForm.imageUrl = item.imageUrl || ''
      this.dialogForm.videoData = {
        videoUrl: item.videoUrl || '',
        coverUrl: item.videoCoverUrl || '',
      }

      // 为社群素材设置内容类型
      if (type === 'community') {
        if (item.videoUrl) {
          this.dialogForm.contentType = 'video'
        } else if (item.imageUrl) {
          this.dialogForm.contentType = 'image'
        } else {
          this.dialogForm.contentType = 'text'
        }
      }

      this.contentDialogVisible = true
    },

    // 通用删除内容项目
    removeContentItem(index, type) {
      this.$confirm('确定要删除这个内容吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          const lists = {
            selection: this.selectionProcessList,
            community: this.communityMaterialList,
          }
          lists[type].splice(index, 1)
          this.$message.success('删除成功')
        })
        .catch(() => {})
    },

    // 兼容性方法（调用通用方法）
    openSelectionDialog() {
      this.openContentDialog('selection')
    },
    openCommunityDialog() {
      this.openContentDialog('community')
    },
    editSelectionItem(index) {
      this.editContentItem(index, 'selection')
    },
    editCommunityItem(index) {
      this.editContentItem(index, 'community')
    },
    removeSelectionItem(index) {
      this.removeContentItem(index, 'selection')
    },
    removeCommunityItem(index) {
      this.removeContentItem(index, 'community')
    },

    // 通用保存内容方法
    saveDialogContent() {
      // 社群素材的内容验证
      if (this.dialogType === 'community') {
        if (this.dialogForm.contentType === 'text' && !this.dialogForm.text.trim()) {
          this.$message.error('请输入文本内容')
          return
        }
        if (this.dialogForm.contentType === 'image' && !this.dialogForm.imageUrl) {
          this.$message.error('请上传图片')
          return
        }
        if (this.dialogForm.contentType === 'video' && !this.dialogForm.videoData.videoUrl) {
          this.$message.error('请上传视频')
          return
        }
      } else {
        // 严选过程的验证（保持原有逻辑）
        if (!this.dialogForm.title.trim()) {
          this.$message.error('请输入标题')
          return
        }
        if (!this.dialogForm.text.trim()) {
          this.$message.warning('请输入文本内容')
          return
        }
      }

      const contentItem = {}

      // 严选过程：保存所有字段
      if (this.dialogType === 'selection') {
        contentItem.title = this.dialogForm.title.trim()
        contentItem.text = this.dialogForm.text.trim()
        contentItem.imageUrl = this.dialogForm.imageUrl || ''
        contentItem.videoUrl = this.dialogForm.videoData.videoUrl || ''
        contentItem.videoCoverUrl = this.dialogForm.videoData.coverUrl || ''
      } else {
        // 社群素材：根据内容类型保存对应字段
        if (this.dialogForm.contentType === 'text') {
          contentItem.text = this.dialogForm.text.trim()
          contentItem.imageUrl = ''
          contentItem.videoUrl = ''
          contentItem.videoCoverUrl = ''
        } else if (this.dialogForm.contentType === 'image') {
          contentItem.text = ''
          contentItem.imageUrl = this.dialogForm.imageUrl
          contentItem.videoUrl = ''
          contentItem.videoCoverUrl = ''
        } else if (this.dialogForm.contentType === 'video') {
          contentItem.text = ''
          contentItem.imageUrl = ''
          contentItem.videoUrl = this.dialogForm.videoData.videoUrl
          contentItem.videoCoverUrl = this.dialogForm.videoData.coverUrl || ''
        }
      }

      const lists = {
        selection: this.selectionProcessList,
        community: this.communityMaterialList,
      }

      const targetList = lists[this.dialogType]

      if (this.editingIndex === -1) {
        // 新增
        targetList.push(contentItem)
      } else {
        // 编辑
        this.$set(targetList, this.editingIndex, contentItem)
      }

      this.contentDialogVisible = false
      this.$message.success(this.editingIndex === -1 ? '添加成功' : '编辑成功')
    },

    // 兼容性方法（调用通用方法）
    saveSelectionContent() {
      this.saveDialogContent()
    },
    saveCommunityContent() {
      this.saveDialogContent()
    },

    // 内容类型切换处理
    onContentTypeChange(newType) {
      // 切换内容类型时清空其他类型的数据
      if (newType === 'text') {
        this.dialogForm.imageUrl = ''
        this.dialogForm.videoData = {
          videoUrl: '',
          coverUrl: '',
        }
      } else if (newType === 'image') {
        this.dialogForm.text = ''
        this.dialogForm.videoData = {
          videoUrl: '',
          coverUrl: '',
        }
      } else if (newType === 'video') {
        this.dialogForm.text = ''
        this.dialogForm.imageUrl = ''
      }
    },

    // 重置弹出层
    resetDialog() {
      this.dialogForm.title = ''
      this.dialogForm.text = ''
      this.dialogForm.imageUrl = ''
      this.dialogForm.videoData = {
        videoUrl: '',
        coverUrl: '',
      }
      this.dialogForm.contentType = 'text'
      this.editingIndex = -1
      this.dialogType = ''
    },

    // 处理视频上传错误
    handleVideoUploadError(error) {
      this.$message.error('上传失败：' + error.message)
    },
    // 格式化价格显示
    formatPrice(price) {
      return (price / 100).toFixed(2)
    },
    // 格式化佣金比例显示
    formatCommissionRatio(ratio) {
      return (ratio / 10000).toFixed(2) + '%'
    },
    // 关闭编辑器
    close() {
      location.reload()
      //this.$router.push({ path: '/product/choseproduct' })
    },
    // 通用图片上传成功处理
    handleUploadSuccess(response, file, fileList, type = 'main') {
      // XUpload 组件已经通过 v-model 自动更新了 uploadedImages 数组
      // 这里只需要同步到表单字段即可
      this.syncImages(type)
    },
    // 通用图片删除处理
    handleUploadRemove(file, fileList, type = 'main') {
      const targetArray = type === 'main' ? this.uploadedImages : this.uploadedDescImages
      const index = targetArray.findIndex((url) => url === file.url)
      if (index > -1) {
        targetArray.splice(index, 1)
        this.syncImages(type)
      }
    },
    // 描述图片上传成功处理（兼容性方法）
    handleDescUploadSuccess(response, file, fileList) {
      this.handleUploadSuccess(response, file, fileList, 'desc')
    },
    // 描述图片删除处理（兼容性方法）
    handleDescUploadRemove(file, fileList) {
      this.handleUploadRemove(file, fileList, 'desc')
    },
    // 添加描述图片链接
    addDescImageUrl() {
      this.addImageUrl('desc')
    },
    // 描述图片操作方法（调用通用方法）
    moveDescImageLeft(index) {
      this.moveImageLeft(index, 'desc')
    },
    moveDescImageRight(index) {
      this.moveImageRight(index, 'desc')
    },
    removeDescImage(index) {
      this.removeImage(index, 'desc')
    },
    handleDescImageError(index) {
      this.handleImageError(index, 'desc')
    },
  },
}
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.editor-mode {
  font-weight: bold;
  font-size: 14px;
}

.source-code-textarea {
  width: 100%;
  height: 800px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  outline: none;
  resize: vertical;
  background: #f9f9f9;
}
.source-code-textarea:focus {
  border-color: #409eff;
  background: #fff;
}

.commission-amount {
  color: #e6a23c;
  font-weight: bold;
  font-size: 16px;
}

/* 移除重复的样式定义 */

.image-preview-list {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 15px;
  padding: 15px;
  background: #f9f9f9;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.image-item {
  position: relative;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.image-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.preview-image {
  width: 120px;
  height: 120px;
  object-fit: cover;
  display: block;
}

/* 重复样式已在下方统一定义 */

.content-list {
  margin-bottom: 15px;
  padding: 15px;
  background: #f9f9f9;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.content-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 0;
  margin-bottom: 15px;
  background: white;
  position: relative;
  display: flex;
  align-items: stretch;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
  min-height: 80px;
}

.content-item:last-child {
  margin-bottom: 0;
}

.content-item:hover {
  border-color: #409eff;
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.2);
  transform: translateY(-2px);
}

.content-index {
  background: linear-gradient(135deg, #409eff 0%, #667eea 100%);
  color: white;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 16px;
  margin: 15px;
  border-radius: 50%;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.content-body {
  flex: 1;
  padding: 15px 0;
  min-width: 0;
}

.content-text {
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 12px;
  color: #303133;
  word-wrap: break-word;
}

.content-image {
  margin-bottom: 12px;
}

.content-preview-image {
  max-width: 180px;
  max-height: 120px;
  border-radius: 6px;
  border: 1px solid #dcdfe6;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  object-fit: cover;
}

.content-preview-image:hover {
  transform: scale(1.05);
}

.content-actions {
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 6px;
  align-items: center;
  justify-content: center;
  border-left: 1px solid #f0f0f0;
  background: #fafbfc;
  min-width: 80px;
  flex-shrink: 0;
}

.content-actions .el-button {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  min-width: 50px;
  width: 100%;
  margin: 0;
}

.content-actions .delete-action {
  color: #f56c6c;
}

.content-actions .delete-action:hover {
  color: #f56c6c;
  background: #fef0f0;
  border-color: #fbc4c4;
}

/* 内容项响应式优化 */
.content-item .content-body {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.content-item .content-actions {
  align-self: stretch;
}

/* 当内容较少时，确保按钮居中 */
.content-item:not(:has(.content-image)):not(:has(.content-video)) .content-actions {
  justify-content: center;
}

/* 严选过程特定样式 */
.selection-content-item .content-index {
  background: linear-gradient(135deg, #f56c6c 0%, #ff7875 100%);
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
}

.selection-content-item:hover {
  border-color: #f56c6c;
  box-shadow: 0 4px 16px rgba(245, 108, 108, 0.2);
}

/* 社群素材特定样式 */
.community-content-item .content-index {
  background: linear-gradient(135deg, #909399 0%, #a6a9ad 100%);
  box-shadow: 0 2px 8px rgba(144, 147, 153, 0.3);
}

.community-content-item:hover {
  border-color: #909399;
  box-shadow: 0 4px 16px rgba(144, 147, 153, 0.2);
}

/* 空状态样式 */
.empty-content {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
  background: #fafbfc;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  margin-bottom: 15px;
}

.empty-content i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
  opacity: 0.6;
}

.empty-content p {
  margin: 0;
  font-size: 14px;
  line-height: 1.6;
}
/* 基本信息卡片美化 */
.info-card {
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.info-card .el-card__header {
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e7ed 100%);
  border-bottom: 1px solid #e4e7ed;
  padding: 15px 20px;
}

/* 严选过程卡片美化 */
.selection-card {
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.selection-card .el-card__header {
  background: linear-gradient(135deg, #fef0f0 0%, #fde2e2 100%);
  border-bottom: 1px solid #f5c6cb;
  padding: 15px 20px;
}

/* 社群素材卡片美化 */
.community-card {
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.community-card .el-card__header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
  padding: 15px 20px;
}

/* 只读标签样式 */
.readonly-label {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  min-height: 32px;
  box-sizing: border-box;
}

.readonly-label .readonly-icon {
  color: #909399;
  margin-right: 8px;
  font-size: 14px;
}

.readonly-label span {
  flex: 1;
  word-break: break-all;
}

/* 只读标题样式 */
.readonly-title {
  min-height: 40px;
  align-items: flex-start;
  padding: 10px 12px;
}

.readonly-title span {
  line-height: 1.6;
  word-wrap: break-word;
  white-space: pre-wrap;
}

/* 价格样式 */
.readonly-price {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-color: #bae6fd;
  color: #0c4a6e;
}

.price-symbol {
  color: #0369a1;
  font-weight: bold;
  margin-right: 4px;
}

.price-value {
  font-weight: 600;
  font-size: 15px;
}

.commission-amount {
  color: #e6a23c;
  font-weight: bold;
  font-size: 16px;
}

/* 悬停效果 */
.readonly-label:hover {
  background-color: #f0f2f5;
  border-color: #c0c4cc;
}

.readonly-price:hover {
  background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%);
  border-color: #7dd3fc;
}

/* 开关样式 */
.switch-row .el-form-item {
  margin-bottom: 0;
}

.switch-row .el-switch__label {
  color: #606266;
}

.switch-row .el-switch__label.is-active {
  color: #409eff;
}

/* 表单提示样式 */
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

/* 卡片整体美化 */
.section-card {
  margin-bottom: 20px;
  border-radius: 8px;
}

.section-card .el-card__body {
  padding: 20px;
}

.upload-section {
  margin-bottom: 20px;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
}

.add-url-section {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.image-preview-list {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 15px;
}

.image-item {
  position: relative;
  width: 120px;
  height: 120px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  background: #f5f7fa;
  display: inline-block;
}

.image-index {
  position: absolute;
  top: 5px;
  left: 5px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  z-index: 2;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 6px;
  opacity: 0;
  transition: opacity 0.3s;
  height: 28px;
  box-sizing: border-box;
}

.image-item:hover .image-actions {
  opacity: 1;
}

.image-actions .el-button {
  padding: 2px 4px;
  font-size: 10px;
  color: white;
  min-width: 24px;
  height: 20px;
  line-height: 16px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.image-actions .move-btn {
  width: 20px;
  min-width: 20px;
  padding: 2px;
}

.image-actions .move-left {
  margin-right: auto;
}

.image-actions .move-right {
  margin-left: auto;
}

.image-actions .delete-btn {
  color: #f56c6c;
  width: 40px;
  min-width: 40px;
}

.image-actions .el-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.image-actions .delete-btn:hover {
  color: #ff4757;
  background: rgba(255, 71, 87, 0.2);
}

.image-actions .el-button .el-icon {
  font-size: 10px;
}

.image-actions .el-button[disabled] {
  color: #666;
  cursor: not-allowed;
  background: rgba(255, 255, 255, 0.05);
}

.image-actions .el-button[disabled]:hover {
  color: #666;
  background: rgba(255, 255, 255, 0.05);
}

/* 描述图片区域样式已合并到通用图片样式中 */

/* 内容弹出层样式 */
.content-dialog .el-dialog__header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 24px;
  border-radius: 8px 8px 0 0;
}

.content-dialog .el-dialog__title {
  color: white;
  font-weight: 600;
  font-size: 16px;
}

.content-dialog .el-dialog__headerbtn .el-dialog__close {
  color: white;
  font-size: 18px;
}

.content-dialog .el-dialog__body {
  padding: 24px;
  background: #fafbfc;
}

.content-dialog .el-dialog__footer {
  padding: 16px 24px;
  background: white;
  border-top: 1px solid #e8eaec;
  text-align: right;
}

.dialog-form .el-form-item {
  margin-bottom: 24px;
}

.dialog-form .el-form-item__label {
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.6;
}

.text-content-item .el-form-item__content {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-content-item .image-upload-wrapper {
  padding: 16px;
  background: white;
  border-radius: 8px;
  border: 2px dashed #d9d9d9;
  transition: border-color 0.3s;
}

.image-content-item .image-upload-wrapper:hover {
  border-color: #409eff;
}

.image-upload-wrapper .upload-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
  text-align: center;
}

.dialog-footer .el-button {
  padding: 10px 20px;
  border-radius: 6px;
  font-weight: 500;
}

.dialog-footer .el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.dialog-footer .el-button--primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* 内容标题样式 */
.content-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
  padding: 8px 12px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.content-video {
  margin-top: 10px;
}

.video-player {
  width: 100%;
  max-width: 300px;
  max-height: 200px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  object-fit: cover;
}

.video-upload-wrapper {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.video-content-item,
.video-cover-item {
  margin-bottom: 20px;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

/* 多图显示样式 */
.multi-image-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.multi-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  transition: transform 0.2s;
}

.multi-image:hover {
  transform: scale(1.05);
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

/* 当只有一张图片时的样式 */
.content-image .content-preview-image:not(.multi-image) {
  max-width: 200px;
  max-height: 150px;
  object-fit: cover;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  margin-top: 8px;
}
</style>
