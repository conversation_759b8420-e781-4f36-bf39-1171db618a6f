<template>
  <x-dialog
    :title="title"
    :visible.sync="visible"
    v-loading="loading"
    @submit="submitForm"
    @cancel="close"
  >
    <el-form ref="form" :model="form" label-width="100px" size="medium">
      <el-row>
        <el-col :span="12">
          <el-form-item label="平台标识" prop="id">
            <el-input :disabled="isEdit" v-model="form.id" placeholder="请输入平台标识" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="平台名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入平台名称" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="平台描述" prop="description">
            <el-input
              type="textarea"
              v-model="form.description"
              placeholder="请输入描述"
              rows="4"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="排序" prop="sort">
            <el-input-number v-model="form.sort" placeholder="请输入排序，越大越前" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </x-dialog>
</template>

<script>
import { platformApi } from '@/api'
export default {
  data() {
    return {
      title: '',
      visible: false,
      loading: false,
      isEdit: false,
      form: {},
    }
  },
  methods: {
    add() {
      this.reset()
      this.title = '新增'
      this.visible = true
      this.isEdit = false
    },
    async edit(row) {
      this.reset()
      this.title = '修改'
      this.visible = true
      this.isEdit = true
      this.$xloading.show()
      const res = await platformApi.getDetail(row.id)
      if (res.code == 0) {
        this.form = res.data
      }
      this.$xloading.hide()
    },

    close() {
      this.reset()
      this.visible = false
    },

    reset() {
      this.form = {}
      this.$xResetForm('form')
    },

    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        // console.log('submitForm valid: ', valid, 'data: ', this.form)
        if (valid) {
          this.$xloading.show()
          const data = Object.assign({}, this.form)
          if (this.isEdit) {
            const res = await platformApi.editPlatform(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('修改成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          } else {
            const res = await platformApi.addPlatform(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('添加成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          }
        }
      })
    },
  },
}
</script>

<style></style>
