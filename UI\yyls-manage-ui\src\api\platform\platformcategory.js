import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/platformCategory/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/platformCategory/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function addPlatformCategory(data) {
  return request({
    url: '/platformCategory/add',
    method: 'post',
    data: data,
  })
}

export function editPlatformCategory(data) {
  return request({
    url: '/platformCategory/edit',
    method: 'post',
    data: data,
  })
}

export function delPlatformCategory(id) {
  return request({
    url: '/platformCategory/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

export function syncPlatformCategory(platformId) {
  return request({
    url: '/platformCategory/sync',
    method: 'post',
    data: {
      platformId,
    },
  })
}

//----------PlatformCategory结束----------
